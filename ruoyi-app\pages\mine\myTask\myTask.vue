<template>
	<view class="container">
		<z-paging ref="paging" v-model="taskList" @query="getTaskList" :fixed="false">
			<template #top>
				<rui-nav-bar title="我的任务"></rui-nav-bar>
			</template>

			<view class="tasks">
				<view class="task-item" v-for="(item,index) in taskList" :key="index" @click="navigateToDetail(item.id)">
					<!-- 任务卡片 -->
					<view class="task-card">
						<!-- 任务头部 -->
						<view class="task-header">
							<view class="task-title">{{ item.title }}</view>
							<view class="task-status" :class="['status-' + item.status]">
								{{ getStatusText(item.status) }}
							</view>
						</view>
						
						<!-- 任务信息 -->
						<view class="task-info">
							<view class="info-item">
								<text class="info-label">奖励积分:</text>
								<text class="info-value reward">{{ item.rewardPoints }}分</text>
							</view>
							<view class="info-item">
								<text class="info-label">接取时间:</text>
								<text class="info-value">{{ item.acceptTime }}</text>
							</view>
						</view>
						
						<!-- 任务进度条（针对进行中或待审核的任务） -->
						<view class="progress-section" v-if="item.status === 1 || item.status === 2">
							<view class="progress-bar-wrapper">
								<view class="progress-bar-fill" :style="{ width: getProgressWidth(item.status) }"></view>
							</view>
							<text class="progress-label">{{ getProgressText(item.status) }}</text>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { myTask } from "@/api/common.js"
	
	export default {
		data() {
			return {
				taskList: [],
				queryParams: {
					pageSize: 10,
					pageNum: 1
				}
			}
		},
		methods: {
			// 获取任务列表
			getTaskList(pageNo, pageSize) {
				myTask({ pageNum: pageNo, pageSize }).then(res => {
					this.$refs.paging.complete(res.rows || []);
				}).catch(err => {
					this.$refs.paging.complete([]);
					console.error('获取任务列表失败:', err);
				})
			},
			
			getStatusText(status) {
				const statusMap = { 1: '进行中', 2: '审核中', 3: '已完成', 4: '未通过', 5: '已放弃' };
				return statusMap[status] || '未知';
			},
			
			getProgressWidth(status) {
				const progressMap = { 1: '30%', 2: '85%' }; 
				return progressMap[status] || '0%';
			},
			
			getProgressText(status) {
				const textMap = { 1: '努力完成中...', 2: '等待平台审核' };
				return textMap[status] || '';
			},

			navigateToDetail(id) {
				uni.navigateTo({ url: `/pages/mine/myTaskDetail/myTaskDetail?id=${id}` });
			}
		}
	}
</script>

<style lang="scss">
	.container {
		height: 100vh;
		padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
		box-sizing: border-box;
		background: #f5f6fa;
	}

	.tasks {
		padding: 20rpx 28rpx;
	}
	
	.task-item {
		margin-bottom: 24rpx;
	}

	.task-card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 8rpx;
			height: 100%;
			background: linear-gradient(to bottom, #409eff, #66b1ff);
		}
		
		&:active {
			transform: scale(0.98);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		}
	}

	.task-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 16rpx;
		padding-left: 16rpx;
	}

	.task-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
		line-height: 1.4;
		flex: 1;
		margin-right: 20rpx;
	}

	.task-status {
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: 500;
		white-space: nowrap;
		
		&.status-1 {
			background: linear-gradient(135deg, #409eff, #66b1ff);
			color: white;
		}
		
		&.status-2 {
			background: linear-gradient(135deg, #e6a23c, #f0b760);
			color: white;
		}
		
		&.status-3 {
			background: linear-gradient(135deg, #67c23a, #85ce61);
			color: white;
		}
		
		&.status-4 {
			background: linear-gradient(135deg, #f56c6c, #f78989);
			color: white;
		}
		
		&.status-5 {
			background: linear-gradient(135deg, #909399, #a6a9ad);
			color: white;
		}
	}

	.task-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		flex-wrap: wrap;
		gap: 12rpx;
		padding-left: 16rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.info-label {
		font-size: 24rpx;
		color: #95a5a6;
	}

	.info-value {
		font-size: 24rpx;
		color: #2c3e50;
		font-weight: 500;
		
		&.reward {
			color: #409eff;
			font-weight: 600;
			font-size: 26rpx;
		}
	}

	.progress-section {
		margin-bottom: 16rpx;
		padding-left: 16rpx;
	}

	.progress-bar-wrapper {
		width: 100%;
		height: 6rpx;
		background: #ecf0f1;
		border-radius: 3rpx;
		overflow: hidden;
		margin-bottom: 6rpx;
	}

	.progress-bar-fill {
		height: 100%;
		background: linear-gradient(90deg, #409eff, #66b1ff);
		border-radius: 3rpx;
		transition: width 0.3s ease;
	}

	.progress-label {
		font-size: 22rpx;
		color: #95a5a6;
	}
</style>