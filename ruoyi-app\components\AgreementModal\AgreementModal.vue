<template>
	<!-- 用户隐私协议提示弹窗组件 -->
	<uni-popup ref="modal" type="bottom" :mask-click="false" :safeArea="true" backgroundColor="#fff" borderRadius="36rpx 36rpx 0 0">
		<view class="agreement-modal">
			<view class="modal-title">温馨提示</view>
			<view class="modal-content">
				<p>亲爱的用户，欢迎使用霸王茶姬小程序</p>
				<p>我们依据相关法律法规制定了<text>《霸王茶姬用户协议》</text>和<text>《霸王茶姬隐私协议》</text>，请您在使用我们的产品前仔细阅读并充分理解相关条款，以了解您的权利。</p>
				<p>根据《常见类型移动互联网应用程序必要个人信息范围规定》，霸王茶姬
					小程序属于网上购物类，基本功能为“购买商品”，必要个人信息包括:注册用户移动电话号码;收货人姓名(名称)、地址、联系电话;支付时间、支付金额、支付渠道等支付信息。</p>
				<p>
					我们严格遵循最小必要原则，在法律规定的必要信息范围内及与实现业务相关联的个人信息范围内处理个人信息。您可以通过《霸王茶姬用户隐私政策》了解我们处理您个人信息的情况，以及您所享有的相关权利。如您是未成年人，请您和您的监护人仔细阅读本政策，并在征得您的监护人授权同意的前提下使用我们的服务或向我们提供个人信息。
				</p>
				<p>您同意《霸王茶姬用户隐私政策》仅代表您已了解应用提供的功能，以及功能运行所需的必要个人信息，并不代表您已同意我们可以收集非必要个人信息，非必要个人信息会根据您使用过程中的授权情况单独征求您的同意</p>
				<p>设备权限不会默认开启。您在使用具体业务功能时，我们会弹窗申请相关设备权限，征得您的同意后开启:权限开启后您可随时关闭权限:您不同意开启权限，将不会影响其他非相关业务功能的正常使用</p>
				<p>如您不同意《霸王茶姬用户隐私政策》，我们为您提供了仅浏览的访客模式。访客模式下，您可浏览我们的商品与服务，但无法使用我们的完整功能。为了给您更优的用户体验，推荐您使用完整功能模式、</p>
			</view>
			<view class="modal-foot">
				<rui-button :plain="true" @click="refusePrivacy">拒绝</rui-button>
				<rui-button @click="agreePrivacy">同意</rui-button>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		name: "AgreementModal",
		data() {
			return {
				isPrivacyAgreed: 1,
			};
		},
		mounted() {
			this.isPrivacyAgreed = uni.getStorageSync("isPrivacyAgreed") || -1;
			if(this.isPrivacyAgreed===-1){
				this.$refs.modal.open();
			}
		},
		methods: {
			refusePrivacy() {
				this.isPrivacyAgreed = 2;
				uni.setStorageSync("isPrivacyAgreed", 2);
				this.closeModal();
			},
			agreePrivacy() {
				this.isPrivacyAgreed = 1;
				uni.setStorageSync("isPrivacyAgreed", 1);
				this.closeModal();
			},
			closeModal() {
				this.$refs.modal.close();
			},
		}
	}
</script>

<style lang="scss">
	.agreement-modal {
		height: 60vh;
		display: flex;
		flex-direction: column;

		.modal-title {
			text-align: center;
			font-size: 36rpx;
			font-weight: bold;
			padding: 40rpx 30rpx 30rpx;
			color: #333333;
		}

		.modal-content {
			flex: 1;
			overflow-y: scroll;
			padding: 0 30rpx;
			color: #333333;
			font-weight: 400;

			p {
				margin-bottom: 20rpx;
				line-height: 1.5;
				font-size: 24rpx;

				text {
					color: $uni-color-primary;
					cursor: pointer;
				}
			}
		}

		.modal-foot {
			display: grid;
			grid-template-columns: 1fr 1fr;
			padding: 20rpx 50rpx 0;
			grid-gap: 30rpx;
		}
	}
</style>