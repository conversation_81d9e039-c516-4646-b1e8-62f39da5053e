<template>
	<view class="rui-cell" :class="{'isArrow':arrow}" :style="style"  @click="$emit('click')">
		<view class="rui-cell_label">
			<slot name="label">{{label}}</slot>
		</view>
		<view class="rui-cell_content">
			<slot>
				<block v-if="!content">
					<text class="rui-cell_placeholder">{{placeholder}}</text>
				</block>
				<block v-else>{{content}}</block>
			</slot>
		</view>
		<view class="rui-cell_arrow" v-if="arrow">
			<rui-icon name="right" size="24"></rui-icon>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-cell",
		emits: ['click'],
		props: {
			label: {
				type: String,
				default: ''
			},
			content: {
				type: String,
				default: ''
			},
			placeholder:{
				type: String,
					default: ''
				},
			arrow: {
				type: Boolean,
				default: false
			},
			disabled: {
				type: <PERSON>olean,
				default: false
			},
			borderTop: {
				type: <PERSON>olean,
				default: true
			},
			radius:{
				type: String,
				default: '0rpx'
			},
			bg:{
				type: String,
				default: 'transparent'
			},
			padding:{
				type: String,
				default: '34rpx 0'
			},
			height:{
				type: String,
				default: 'auto'
			}
		},
		computed:{
			style(){
				return `height:${this.height};background:${this.bg};border-radius:${this.radius};padding:${this.padding}`
			}
		}
	}
</script>

<style lang="scss">
	.rui-cell {
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		.rui-cell_content {
			flex: 1;
			text-align: right;
			color: #646566;
			font-size: 26rpx;
			.rui-cell_placeholder{
				color: #c8c9cc;
				font-size: 24rpx;
			}
		}

		.rui-cell_label {
			flex-shrink: 0;
			font-size: 24rpx;
			color: #323233;
		}

		.rui-cell_arrow {
			flex-shrink: 0;
			margin-left: 16rpx;
		}
	}
	.isArrow:active{
		opacity: .9;
		transition: all .2s;
	}
</style>