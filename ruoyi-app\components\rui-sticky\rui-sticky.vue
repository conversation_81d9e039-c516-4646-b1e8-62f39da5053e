<template>
	<view class="rui-sticky" :style="style">
		<slot></slot>
	</view>
</template>

<script>
	export default {
		name:"rui-sticky",
		computed:{
			style(){
				return `
					top: ${this.top};
					background-color: ${this.background};
					z-index: ${this.zIndex};
				`
			}
		},
		props:{
			top:{
				type:String,
				default:"0"
			},
			zIndex:{
				type:[Number,String],
				default:9
			},
			background:{
				type:String,
				default:"transparent"
			},
			
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">
	.rui-sticky{
		 position: -webkit-sticky;
		 position: sticky;
	}
</style>