<template>
	<view class="verification-container">
		<rui-nav-bar title="核销管理" back></rui-nav-bar>
		
		<view class="content">
			<!-- 页面标题和说明 -->
			<view class="header-section">
				<view class="title">订单核销</view>
				<view class="subtitle">扫描用户的兑换码进行核销操作</view>
			</view>
			
			<!-- 扫码区域 -->
			<view class="scan-section">
				<view class="scan-icon-wrapper">
					<image src="../../../static/images/icon/scan-full.png" class="scan-icon"></image>
				</view>
				<button class="scan-button" @click="handleScan">
					<text class="scan-text">点击扫码核销</text>
				</button>
			</view>
			
			<!-- 手动输入区域 -->
			<!-- <view class="manual-section">
				<view class="manual-title">手动输入订单号</view>
				<view class="input-wrapper">
					<input 
						class="order-input" 
						v-model="orderId" 
						placeholder="请输入订单号"
						maxlength="50">
					<button class="verify-button" @click="handleVerification" :disabled="!orderId">核销</button>
				</view>
			</view> -->
			
			<!-- 核销记录 -->
			<!-- <view class="history-section" v-if="recentRecords.length">
				<view class="history-title">最近核销记录</view>
				<view class="record-list">
					<view class="record-item" v-for="(item, index) in recentRecords" :key="index">
						<view class="record-info">
							<text class="record-order">{{item.orderId}}</text>
							<text class="record-time">{{item.time}}</text>
						</view>
						<view class="record-status success">已核销</view>
					</view>
				</view>
			</view> -->
		</view>
		
		<!-- 加载状态 -->
		<uni-loading v-if="loading" :text="loadingText"></uni-loading>
	</view>
</template>

<script>
	import { verification } from "@/api/common.js"
	export default {
		data() {
			return {
				orderId: '',
				loading: false,
				loadingText: '核销中...',
				recentRecords: [] // 最近核销记录
			}
		},
		onLoad() {
			this.loadRecentRecords()
		},
		methods: {
			// 扫码核销
			handleScan() {
				uni.scanCode({
					success: (res) => {
						console.log('扫描结果:', res);
						console.log('扫描到的内容:', res.result);
						
						// 解析扫码结果获取orderId
						let scannedOrderId = res.result
						
						if (scannedOrderId) {
							this.orderId = scannedOrderId
							this.handleVerification()
						} else {
							uni.showToast({
								title: '无法识别的二维码',
								icon: 'none',
								duration: 2000
							})
						}
					},
					fail: (err) => {
						console.error('扫码失败:', err)
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'none'
						})
					}
				});
			},
			
			// 解析订单ID
			parseOrderId(result) {
				// 如果扫描结果直接就是订单ID
				if (result && typeof result === 'string' && result.trim()) {
					return result.trim()
				}
				
				// 如果是JSON格式，尝试解析
				try {
					const parsed = JSON.parse(result)
					if (parsed.orderId) {
						return parsed.orderId
					}
				} catch (e) {
					// 不是JSON格式，继续其他解析方式
				}
				
				// 如果是URL格式，尝试提取orderId参数
				try {
					const url = new URL(result)
					const orderId = url.searchParams.get('orderId')
					if (orderId) {
						return orderId
					}
				} catch (e) {
					// 不是URL格式
				}
				
				return result // 直接返回原始结果
			},
			
			// 执行核销
			async handleVerification() {
				if (!this.orderId.trim()) {
					uni.showToast({
						title: '请输入订单号',
						icon: 'none'
					})
					return
				}
				
				this.loading = true
				this.loadingText = '核销中...'
				
				try {
					const result = await verification(this.orderId.trim())
					
					if (result.code === 200) {
						// 核销成功
						uni.showToast({
							title: '核销成功',
							icon: 'success',
							duration: 2000
						})
						
						// 添加到核销记录
						this.addToRecentRecords(this.orderId.trim())
						
						// 清空输入框
						this.orderId = ''
						
					} else {
						// 核销失败
						uni.showToast({
							title: result.msg || '核销失败',
							icon: 'none',
							duration: 3000
						})
					}
				} catch (error) {
					console.error('核销异常:', error)
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none',
						duration: 2000
					})
				} finally {
					this.loading = false
				}
			},
			
			// 添加到最近记录
			addToRecentRecords(orderId) {
				const now = new Date()
				const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
							   now.getMinutes().toString().padStart(2, '0')
				
				const record = {
					orderId: orderId,
					time: timeStr
				}
				
				// 添加到数组开头
				this.recentRecords.unshift(record)
				
				// 只保留最近10条记录
				if (this.recentRecords.length > 10) {
					this.recentRecords = this.recentRecords.slice(0, 10)
				}
				
				// 保存到本地存储
				this.saveRecentRecords()
			},
			
			// 加载最近记录
			loadRecentRecords() {
				try {
					const records = uni.getStorageSync('verification_records')
					if (records) {
						this.recentRecords = JSON.parse(records)
					}
				} catch (e) {
					console.error('加载核销记录失败:', e)
				}
			},
			
			// 保存最近记录
			saveRecentRecords() {
				try {
					uni.setStorageSync('verification_records', JSON.stringify(this.recentRecords))
				} catch (e) {
					console.error('保存核销记录失败:', e)
				}
			}
		}
	}
</script>

<style lang="scss">
.verification-container {
	min-height: 100vh;
	background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 50%);
}

.content {
	padding: 32rpx;
}

.header-section {
	text-align: center;
	margin-bottom: 80rpx;
	
	.title {
		font-size: 48rpx;
		font-weight: 600;
		color: #2c3e50;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		font-size: 28rpx;
		color: #7f8c8d;
		line-height: 1.5;
	}
}

.scan-section {
	background: #fff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	text-align: center;
	
	.scan-icon-wrapper {
		margin-bottom: 40rpx;
		
		.scan-icon {
			width: 120rpx;
			height: 120rpx;
		}
	}
	
	.scan-button {
		width: 100%;
		height: 96rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}
		
		.scan-text {
			font-size: 32rpx;
			font-weight: 500;
			color: #fff;
		}
	}
}

.manual-section {
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	
	.manual-title {
		font-size: 30rpx;
		font-weight: 500;
		color: #2c3e50;
		margin-bottom: 32rpx;
	}
	
	.input-wrapper {
		display: flex;
		gap: 20rpx;
		
		.order-input {
			flex: 1;
			height: 80rpx;
			padding: 0 24rpx;
			border: 2rpx solid #e9ecef;
			border-radius: 40rpx;
			font-size: 28rpx;
			color: #333;
			
			&:focus {
				border-color: #667eea;
				outline: none;
			}
		}
		
		.verify-button {
			width: 120rpx;
			height: 80rpx;
			background: #28a745;
			border: none;
			border-radius: 40rpx;
			color: #fff;
			font-size: 28rpx;
			font-weight: 500;
			
			&:disabled {
				background: #ccc;
			}
			
			&:active:not(:disabled) {
				background: #218838;
			}
		}
	}
}

.history-section {
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	
	.history-title {
		font-size: 30rpx;
		font-weight: 500;
		color: #2c3e50;
		margin-bottom: 32rpx;
	}
	
	.record-list {
		.record-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f1f3f4;
			
			&:last-child {
				border-bottom: none;
			}
			
			.record-info {
				flex: 1;
				
				.record-order {
					display: block;
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.record-time {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.record-status {
				padding: 8rpx 16rpx;
				border-radius: 16rpx;
				font-size: 24rpx;
				
				&.success {
					background: #d4edda;
					color: #155724;
				}
			}
		}
	}
}
</style>
