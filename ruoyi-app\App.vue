<script>
  import config from './config'
  import store from '@/store'
  import { getToken } from '@/utils/auth'

  export default {
    onLaunch: function() {
      this.initApp()
    },
    methods: {
      // 初始化应用
      initApp() {
        this.initConfig()
        this.checkLogin()
      },
      initConfig() {
        this.globalData.config = config
      },
      checkLogin() {
        if (getToken()) {
          this.$store.dispatch('user/GetInfo')
        }
      }
    }
  }
</script>

<style lang="scss">
  @import '@/static/scss/index.scss';
</style>
