<template>
	<view :class="['rui-icon','icon-'+name]" :style="{fontSize:size+'rpx',color:color}" @click="handleClick"></view>
</template>

<script>
	export default {
		name: "rui-icon",
		emits: ["click"],
		props: {
			name: String,
			size: {
				type: [Number, String],
				default: 32
			},
			color: {
				type: String,
			},
		},
		methods: {
			handleClick(e) {
				this.$emit('click',e)
			},
		},
	}
</script>

<style lang="scss">

</style>