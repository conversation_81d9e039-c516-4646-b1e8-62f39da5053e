<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList" :fixed="false">
			<template #top>
				<rui-nav-bar title="任务中心"></rui-nav-bar>
			</template>

			<view class="tasks">
				<view class="task-item" v-for="(item,index) in list" :key="index" @click="handleTaskClick(item)">
					<!-- 任务卡片 -->
					<view class="task-card">
						<!-- 任务头部 -->
						<view class="task-header">
							<view class="task-title">{{ item.title }}</view>
							<view class="task-status" :class="statusClassMap[item.status] || 'status-active'">
								{{ statusTextMap[item.status] || '未知' }}
							</view>
						</view>
						
						<!-- 任务信息 -->
						<view class="task-info">
							<view class="info-item">
								<text class="info-label">奖励积分:</text>
								<text class="info-value reward">{{ item.rewardPoints }}分</text>
							</view>
							<view class="info-item">
								<text class="info-label">难度等级:</text>
								<view class="difficulty-level">
									<text class="star" v-for="n in item.difficultyLevel" :key="n">★</text>
									<text class="star-empty" v-for="n in (3 - item.difficultyLevel)" :key="n">☆</text>
								</view>
							</view>
						</view>
						
						<!-- 参与人数信息 -->
						<view class="participants-info" v-if="item.maxParticipants > 1">
							<view class="participants-bar">
								<view class="participants-progress" :style="{ width: (item.currentParticipants / item.maxParticipants * 100) + '%' }"></view>
							</view>
							<text class="participants-text">{{ item.currentParticipants }}/{{ item.maxParticipants }}人参与</text>
						</view>
						
						<!-- 时间信息 -->
						<view class="task-time" v-if="item.startTime || item.endTime">
							<view class="time-item">
								<text class="time-label">开始:</text>
								<text class="time-value">{{ item.startTime }}</text>
								<text class="time-separator">-</text>
								<text class="time-label">结束:</text>
								<text class="time-value">{{ item.endTime }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { getTaskList, acceptTask } from '@/api/common.js'
	import { getToken } from '@/utils/auth'
	
	export default {
		data() {
			return {
				list: [],
				statusClassMap: {
					1: 'status-active',
					2: 'status-paused', 
					3: 'status-completed',
					4: 'status-cancelled'
				},
				statusTextMap: {
					1: '进行中',
					2: '已暂停',
					3: '已完成', 
					4: '已取消'
				},
				btnClassMap: {
					1: 'btn-primary',
					2: 'btn-disabled',
					3: 'btn-completed',
					4: 'btn-disabled'
				}
			}
		},

		onLoad() {},
		
		methods: {
			// 获取任务列表
			getList(pageNo, pageSize) {
				getTaskList({ pageNum: pageNo, pageSize }).then(res => {
					this.$refs.paging.complete(res.rows || []);
				}).catch(err => {
					this.$refs.paging.complete([]);
					console.error('获取任务列表失败:', err);
				})
			},
			
			// 任务点击事件
			handleTaskClick(item) {
				if (!getToken()) {
					this.$store.commit('app/toggleLoginModal', true);
					return;
				}
				// 可以跳转到任务详情页面
				uni.navigateTo({
					url:"/pages/coupon/taskDetail?taskId="+item.id
				})
			},
			
		}
	}
</script>

<style lang="scss">
	.container {
		height: 100vh;
		padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
		box-sizing: border-box;
		background: #f5f6fa;
	}

	.tasks {
		padding: 20rpx 28rpx;
	}
	
	.task-item {
		margin-bottom: 24rpx;
	}

	.task-card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		position: relative;
		overflow: hidden;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 8rpx;
			height: 100%;
			background: linear-gradient(to bottom, #ff6b35, #ff9f43);
		}
		
		&:active {
			transform: scale(0.98);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		}
	}

	.task-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 16rpx;
		padding-left: 16rpx;
	}

	.task-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
		line-height: 1.4;
		flex: 1;
		margin-right: 20rpx;
	}

	.task-status {
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: 500;
		white-space: nowrap;
		
		&.status-active {
			background: linear-gradient(135deg, #ff6b35, #ff9f43);
			color: white;
		}
		
		&.status-paused {
			background: linear-gradient(135deg, #ff9f43, #ffb976);
			color: white;
		}
		
		&.status-completed {
			background: linear-gradient(135deg, #4CAF50, #45a049);
			color: white;
		}
		
		&.status-cancelled {
			background: linear-gradient(135deg, #95a5a6, #7f8c8d);
			color: white;
		}
	}

	.task-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		flex-wrap: wrap;
		gap: 12rpx;
		padding-left: 16rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.info-label {
		font-size: 24rpx;
		color: #95a5a6;
	}

	.info-value {
		font-size: 24rpx;
		color: #2c3e50;
		font-weight: 500;
		
		&.reward {
			color: #ff6b35;
			font-weight: 600;
			font-size: 26rpx;
		}
	}

	.difficulty-level {
		display: flex;
		align-items: center;
		gap: 2rpx;
	}

	.star {
		color: #ff9f43;
		font-size: 22rpx;
	}

	.star-empty {
		color: #ecf0f1;
		font-size: 22rpx;
	}

	.participants-info {
		margin-bottom: 16rpx;
		padding-left: 16rpx;
	}

	.participants-bar {
		width: 100%;
		height: 6rpx;
		background: #ecf0f1;
		border-radius: 3rpx;
		overflow: hidden;
		margin-bottom: 6rpx;
	}

	.participants-progress {
		height: 100%;
		background: linear-gradient(90deg, #ff6b35, #ff9f43);
		border-radius: 3rpx;
		transition: width 0.3s ease;
	}

	.participants-text {
		font-size: 22rpx;
		color: #95a5a6;
	}

	.task-time {
		margin-bottom: 0;
		padding: 12rpx 16rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
		border-left: 4rpx solid #ff9f43;
	}

	.time-item {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8rpx;
	}

	.time-label {
		font-size: 22rpx;
		color: #95a5a6;
	}

	.time-value {
		font-size: 22rpx;
		color: #2c3e50;
		font-weight: 500;
	}

	.time-separator {
		font-size: 22rpx;
		color: #95a5a6;
		margin: 0 8rpx;
	}

	.task-actions {
		display: flex;
		justify-content: flex-end;
		padding-left: 16rpx;
	}

	.action-btn {
		padding: 12rpx 24rpx;
		border-radius: 24rpx;
		font-size: 26rpx;
		font-weight: 500;
		border: none;
		min-width: 140rpx;
		transition: all 0.3s ease;
		
		&.btn-primary {
			background: linear-gradient(135deg, #ff6b35, #ff9f43);
			color: white;
			
			&:active {
				background: linear-gradient(135deg, #ff9f43, #ffb976);
			}
		}
		
		&.btn-completed {
			background: linear-gradient(135deg, #4CAF50, #45a049);
			color: white;
		}
		
		&.btn-disabled {
			background: #ecf0f1;
			color: #95a5a6;
			cursor: not-allowed;
		}
	}
</style>