<template>
	<view class="rui-dict-tag">
		<template v-for="(item, index) in options">
			<rui-tag v-if="values.includes(item.dictValue)" :key="item.dictValue" :type="item.listClass">
				{{ item.dictLabel + ' ' }}
			</rui-tag>
		</template>
		<block v-if="unmatch && showValue">
			{{ unmatchArray | handleArray }}
		</block>
	</view>
</template>

<script>
	export default {
		name: "rui-dict-tag",
		props: {
			options: {
				type: Array,
				default: null,
			},
			value: [Number, String, Array],
			// 当未找到匹配的数据时，显示value
			showValue: {
				type: Boolean,
				default: true,
			},
			separator: {
				type: String,
				default: ","
			}
		},
		data() {
			return {
				unmatchArray: [], // 记录未匹配的项
			}
		},
		computed: {
			values() {
				if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []
				return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)
			},
			unmatch() {
				this.unmatchArray = []
				// 没有value不显示
				if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0)
					return false
				// 传入值为数组
				let unmatch = false // 添加一个标志来判断是否有未匹配项
				this.values.forEach(item => {
					if (!this.options.some(v => v.dictValue === item)) {
						this.unmatchArray.push(item)
						unmatch = true // 如果有未匹配项，将标志设置为true
					}
				})
				return unmatch // 返回标志的值
			},

		},
		filters: {
			handleArray(array) {
				if (array.length === 0) return '';
				return array.reduce((pre, cur) => {
					return pre + ' ' + cur;
				})
			},
		}
	};
</script>

<style lang="scss">
	.rui-tag.rui-tag {
		margin-left: 10px;
	}
</style>