import { login, wxLogin, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const app = {
	namespaced: true,
	state: {
		tabbarIndex: 0,
		loginModal: false,
	},
	mutations: {
		// 设置tabbarIndex
		setTabbarIndex(state, index) {
			state.tabbarIndex = index
		},
		toggleLoginModal(state, loginModal) {
			state.loginModal = loginModal
		},
	}
}

export default app