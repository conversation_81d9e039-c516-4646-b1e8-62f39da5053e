# Description

[问题简单描述]

# Environment

* Platform: [开发者工具/iOS/Andriod/Web]
* Platform version: [对应工具或者iOS或者Andriod的版本号]
* Wechat version: [微信版本号]
* weapp-qrcode version: [在package.json里]
* other version: [如果在某一设备下出现该问题，请填写设备号]

# Reproduce

**问题复现步骤:**

1. [第一步]
2. [第二步]
3. [其他步骤...]

**期望的表现:**

[在这里描述期望的表现]

**观察到的表现:**

[在这里描述观察到的表现]

**屏幕截图和动态 GIF 图**

![复现步骤的屏幕截图和动态 GIF 图](图片的 url)

# Relevant Code / Logs

```
// TODO(you): code or logs here to reproduce the problem
```
