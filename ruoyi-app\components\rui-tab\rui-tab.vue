<template>
	<view class="rui-tab">
		<view :class="{'tab':true,'current-tab':item.value==value}" v-for="(item,index) in _tabs" :key="index"
			@click="handleTab(item)" :style="{background:bg}">{{item.label}}</view>
	</view>
</template>

<script>
	export default {
		name: "rui-tab",
		emits: ["change", "input"],
		computed: {
			_value: {
				get() {
					return this.value 
				},
				set(val) {
					this.$emit("input", val)
				}
			},
			_tabs() {
				return this.tabs.map((item, index) => {
					if (typeof item === 'string') {
						return {
							label: item,
							value: index
						}
					}
					return item
				})
			}
		},
		props: {
			value: {
				type: Number,
				default: 0
			},
			tabs: Array,
			bg:{
				type:String,
				default:"rgba(233, 233, 231, 1)"
			}
		},
		methods: {
			handleTab(item) {
				this._value = item.value
				this.$emit('change', item)
			},
		}
	}
</script>

<style lang="scss">
	.rui-tab {
		display: flex;
		overflow-x: scroll;
		padding: 10rpx 0;

		.tab {
			padding: 0 32rpx;
			height: 56rpx;
			line-height: 56rpx;
			border-radius: 110rpx;
			opacity: 0.6;
			color: rgba(51, 51, 51, 1);
			font-size: 24rpx;
			margin-left: 28rpx;
			flex-shrink: 0;
			transition: background-color .2s;
		}

		.current-tab {
			opacity: 1;
			color: rgba(198, 109, 29, 1);
			background: rgba(252, 234, 212, 1) !important;
			box-shadow: 0px 4rpx 10rpx rgba(252, 234, 212, 0.5);
		}
	}
</style>