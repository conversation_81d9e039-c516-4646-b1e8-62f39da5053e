<template>
	<uni-swiper-dot class="rui-swiper" :info="images" :current="_value" field="content" mode="round"
		:dotsStyles="dotsStyles">
		<swiper :current="_value" :style="{height,'border-radius':radius,overflow: 'hidden'}" :autoplay="autoplay"
			:interval="interval"  :circular="circular" @change="(e)=> _value = e.detail.current">
			<swiper-item v-for="(item ,index) in images" :key="index">
				<view class="image-wrap" :style="{margin,'border-radius':radius}" @click="handleClick(item)">
					<image :src="format_url(item.bannerImageUrl)"></image>
				</view>
			</swiper-item>
		</swiper>
	</uni-swiper-dot>
</template>

<script>
	export default {
		name: "rui-swiper",
		options: { styleIsolation: 'shared' },
		props: {
			value: {
				type: [Number]
			},
			images: {
				type: Array,
				default: () => []
			},
			height: {
				type: String,
				default: "330rpx"
			},
			margin: {
				type: String,
				default: ""
			},
			radius: {
				type: String,
				default: "0"
			},
			interval: {
				type: Number,
				default: 5000
			},
			duration: {
				type: Number,
				default: 500,
			},
			autoplay: {
				type: Boolean,
				default: true
			},
			circular: {
				type: Boolean,
				default: true
			},
		},
		computed: {
			_value: {
				get() {
					return this.value
				},
				set(val) {
					this.$emit("input", val)
				}
			},
			dotsStyles() {
				return {
					'border': "transparent",
					'selectedBorder': "transparent",
					'backgroundColor': '#DBD9D9',
					'selectedBackgroundColor': '#C7C3C3',
				}
			},
		},
		methods: {
			handleClick(item) {
				if (!item.bannerLink) return
				if (item.linkType === '1') {
					// uni.navigateTo({
					// 	url:`/pages/common/webview/index?title=${item.bannerName}&url=${encodeURIComponent(item.bannerLink)}`,
					// })
					wx.openOfficialAccountArticle({
						url:item.bannerLink,
					})
				} else {
					uni.navigateTo({
						url: item.bannerLink,
						fail() {
							uni.switchTab({ url: item.bannerLink })
						}
					})
				}
			},
			format_url(url) {
				const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
				const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
				return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
			}
		}
	}
</script>

<style lang="scss">
	.uni-swiper__warp {
		// border-radius: 24rpx;
	}

	.rui-swiper {
		width: 100%;

		swiper {
			.image-wrap {
				width: 100%;
				height: 100%;
				overflow: hidden;

				image {
					height: 100%;
					width: 100%;
					object-fit: cover;
				}
			}
		}
	}
</style>