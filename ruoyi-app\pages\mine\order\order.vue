<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList">
			<template #top>
				<rui-nav-bar title="我的订单" back></rui-nav-bar>
				<rui-vtab v-model="currentTab" bg="#fff" :tabs="tabs" overspread @change="changeTab"></rui-vtab>
			</template>
			<view class="order-list">
				<view class="goods-box" @click="handleClick(item)" v-for="(item,index) in list" :key="index">
					<rui-image-preview class="goods-cover" :src="item.goods.goodsImage"></rui-image-preview>
					<view class="goods-content">
						<view class="goods-content_title">{{item.goods.goodsName}}</view>
						<view class="goods-content_time">下单时间：{{item.createTime}}</view>
						<view class="goods-content_time">截止时间：{{calculateEndTime(item.createTime,item.goods.effectiveDays)}}</view>
						<view class="goods-content_price">{{item.goods.goodsIntegral * item.goodsCount}} <text
								style="font-size: 20rpx;"> 积分</text> </view>
					</view>
					<!-- <rui-button @click="handleDetail(item)" v-if="item.status==='0'" type="success" height="66rpx" width="180rpx"
						size="24">核销码</rui-button> -->
					<rui-button type="warning" height="66rpx" width="180rpx" size="24"
						@click="handleDetail(item)">查看详情</rui-button>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { listOrder } from "@/api/common.js";
	export default {
		computed: {
			status() {
				if (this.currentTab == '-1') {
					return ""
				}
				return this.currentTab
			}
		},
		data() {
			return {
				tabs: [
					// { label: "全部", value: "-1" },
					{ label: "待兑换", value: "0" },
					{ label: "已完成", value: "1" },
					{ label: "已取消", value: "2" },
				],
				currentTab: "0",
				list: [],
			}
		},
		methods: {
			changeTab(){
				setTimeout(()=>{
					this.$refs.paging.reload()
				},10)
			},
			getList(pageNo, pageSize) {
				listOrder({ pageNum: pageNo, pageSize, status: this.status }).then(res => {
					this.$refs.paging.complete(res.rows);
				})
			},
			calculateEndTime(startTime, daysToAdd) {
				let date = new Date(startTime);
				date.setDate(date.getDate() + daysToAdd);
				const padZero = (num) => (num < 10 ? '0' : '') + num;
				let endYear = date.getFullYear();
				let endMonth = padZero(date.getMonth() + 1); // 月份从0开始，+1表示真实月份
				let endDay = padZero(date.getDate());
				let endHour = padZero(date.getHours());
				let endMinute = padZero(date.getMinutes());
				return `${endYear}.${endMonth}.${endDay} ${endHour}:${endMinute}:00`;
			},
			handleDetail(item) {
				this.$tab.navigateTo(`/pages/mine/order/detail?orderId=${item.orderId}`)
			}
		}
	}
</script>

<style lang="scss">
	page {
		overflow: hidden;
		height: 100vh;
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #fff 36%);
	}

	.container {
		height: 100%;
		overflow: scroll;
	}

	.order-list {
		padding: 20rpx 32rpx;
	}

	.goods-box {
		height: 240rpx;
		border-radius: 28rpx;
		background: rgba(255, 255, 255, 1);
		box-shadow: 0px -3rpx 6rpx rgba(173, 174, 179, 0.09), 0px 3rpx 6px rgba(173, 174, 179, 0.09);
		// padding: 24rpx 24rpx;
		display: flex;
		margin-bottom: 32rpx;
		position: relative;
		overflow: hidden;

		.goods-cover {
			height: 100%;
			width: calc(280rpx - 24rpx - 24rpx);
			flex-shrink: 0;
		}

		.goods-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			position: relative;
			padding: 20rpx 20rpx;

			.goods-content_title {
				font-size: 28rpx;
				font-weight: 500;
				letter-spacing: 0px;
				line-height: 40rpx;
				color: #333333;
				padding-bottom: 5rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.goods-content_time {
				font-size: 26rpx;
			}

			.goods-content_count {
				font-size: 28rpx;

				text {
					font-size: 24rpx;
				}
			}

			.goods-content_price {
				color: #FF6600;
				font-size: 38rpx;
				font-weight: 400;
				position: absolute;
				bottom: 20rpx;
				left: 20rpx;
			}
		}

		.rui-button {
			position: absolute;
			right: 0;
			bottom: 0;
			border-bottom-left-radius: 0 !important;
			border-top-right-radius: 0 !important;
		}
	}
</style>