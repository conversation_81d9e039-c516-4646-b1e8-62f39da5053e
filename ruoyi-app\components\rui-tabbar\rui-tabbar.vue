<template>
	<view class="rui-tabbar">
		<view class="tabbar-item" v-for="(item,index) in tabBar" :key="index" @click="handleTabbar(item,index)">
			<image :src="index===value?item.selectedIconPath:item.iconPath" mode="heightFix"></image>
			<text :style="{color:index===value?activeColor:inactiveColor}">{{item.text}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-tabbar",
		emits: ["change"],
		props: {
			value: {
				type: Number,
				default: 0
			},
			tabBar: {
				type: Array,
				default: () => []
			},
			activeColor: {
				type: String,
				default: '#E0AC7D'
			},
			inactiveColor: {
				type: String,
				default: '#929292'
			},
			backgroundColor: {
				type: String,
				default: '#ffffff'
			},
			height: {
				type: String,
				default: '50px'
			}
		},
		data() {
			return {

			};
		},
		methods: {
			handleTabbar(item, index) {
				if (this.value === index) return
				this.$emit('input', index)
				this.$emit('change', index)
			}
		},
		mounted() {

		},
	}
</script>

<style lang="scss">
	.rui-tabbar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		background-color: #ffffff;
		padding-bottom: env(safe-area-inset-bottom);
		padding-top: 4rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		z-index: 9;
		.tabbar-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 20rpx;

			image {
				height: 50rpx;
				max-width: 56rpx;
			}

			text {
				color: #646566;
				margin-top: 4rpx;
				font-size: 24rpx;
			}
		}
	}
</style>