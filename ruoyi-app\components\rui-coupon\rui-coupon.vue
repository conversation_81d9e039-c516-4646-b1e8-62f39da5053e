<template>
	<view class="rui-coupon" :class="{'rui-coupon_open':open,'rui-coupon_expired':type==='expired'}">
		<view class="coupon-default">
			<view class="coupon-limit">
				<view class="coupon-price">
					<text class="price">{{data.discountAmount}}</text>
					<text class="unit">元</text>
				</view>
				<view class="limit-text">{{data.thresholdAmount===0?'无门槛':"满"+data.thresholdAmount+"元可用"}}</view>
			</view>
			<view class="coupon-info">
				<view class="coupon-title">{{data.packageName}}</view>
				<view class="coupon-date">{{parseTime(data.useStart,'{y}-{m}-{d}')}} -
					{{parseTime(data.useEnd,'{y}-{m}-{d}')}}
				</view>
			</view>
		</view>
		<view class="coupon-line"></view>
		<view class="coupon-content">
			<view class="action-row">
				<view class="row-left" @click="open=!open">
					<view>使用规则</view>
					<view class="icon-bg">
						<uni-icons type="down" color="#9a9a9a" :size="16"></uni-icons>
					</view>
				</view>
				<view class="row-right" v-if="type==='receive'" @click="$emit('click',data)">立即领取</view>
				<view class="row-right" v-else-if="type==='use'" @click="$emit('click',data)">去使用</view>
				<view class="row-right" v-else-if="type==='expired'">已过期</view>
			</view>
			<view class="preview">
				<view class="preview-item">适用门店：指定门店适用 <text class="look" @click="handleLook">查看门店</text></view>
				<view class="preview-item">使用渠道：微信小程序</view>
				<view class="preview-item">使用场景：线下核销</view>
				<view class="preview-item">使用门槛：订单满{{data.thresholdAmount}}元可用</view>
				<view class="preview-item">有效期：{{data.useStart}} ~ {{data.useEnd}}</view>
				<view class="preview-item" v-if="data.remark">优惠券说明：{{data.remark}}</view>
				<view class="preview-item">门店说明：【适用门店】北京市丰台区石榴庄街道榴乡路86号院汇琴购物中心开通汇琴购物中心小程序门店</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-coupon",
		emits: ["click"],
		props: {
			data: {
				type: Object,
				default: () => ({})
			},
			type: {
				type: String,
				default: "receive" // receive领取  use使用 expired过期
			}
		},
		data() {
			return {
				open: false
			};
		},
		methods:{
			handleLook(){
				uni.navigateTo({
					url:'/pages/coupon/stores?packageId='+this.data.packageId
				})
			}
		}
	}
</script>

<style lang="scss">
	.rui-coupon {
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		margin-bottom: 28rpx;
		overflow: hidden;

		.coupon-default {
			padding: 30rpx 0;
			display: flex;
			background-color: #fff;
		}

		.coupon-limit {
			width: 200rpx;
			flex-shrink: 0;
			text-align: center;
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;
			padding: 0 20rpx;

			.coupon-price {
				color: #ff3c46;

				.price {
					font-size: 38rpx;
					font-weight: bold;
				}

				.unit {
					margin-left: 4rpx;
					font-size: 20rpx;
				}
			}

			.limit-text {
				color: #8e8e8e;
				font-size: 24rpx;
			}
		}

		.coupon-info {
			flex: 1;
			padding-right: 40rpx;
			box-sizing: border-box;

			.coupon-title {
				font-size: 32rpx;
				color: #353535;
				line-height: 40rpx;
				font-weight: bold;
			}

			.coupon-date {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #8e8e8e;
			}
		}

		.coupon-line {
			width: 100%;
			height: 16rpx;
			background: radial-gradient(circle at left center, transparent 16rpx, #fff 0) -6rpx center,
				radial-gradient(circle at right center, transparent 16rpx, #fff 0) calc(100% + 6rpx) center;
			background-repeat: no-repeat;
			background-size: 50% 100%;
			position: relative;

			&:before {
				position: absolute;
				left: 16rpx;
				right: 16rpx;
				top: 50%;
				transform: translateY(-50%);
				content: '';
				height: 2rpx;
				background-color: #f3f3f3;
			}
		}

		.coupon-content {
			background-color: #fff;
			padding: 14rpx 20rpx 20rpx;
			box-sizing: border-box;
			font-size: 24rpx;
			border-bottom-left-radius: 20rpx;
			border-bottom-right-radius: 20rpx;
			color: #8e8e8e;

			.action-row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.row-left {
					display: flex;
					align-items: center;
					color: #3a3a3a;

					.icon-bg {
						background-color: #f6f6f6;
						width: 40rpx;
						height: 40rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-left: 18rpx;
						border-radius: 50%;
						transition: all .2s;
					}
				}

				.row-right {
					background-color: #b29060;
					padding: 8rpx 30rpx;
					color: #fff;
					border-radius: 40rpx;
					font-size: 20rpx;
				}
			}

			.preview {
				display: none;

				.preview-item {
					color: #656565;
					font-size: 24rpx;
					line-height: 38rpx;

					.look {
						color: #b29060;
						margin-left: 4rpx;
					}
				}
			}
		}
	}

	.rui-coupon_open {
		.action-row {
			.row-left {
				.icon-bg {
					transform: rotateZ(180deg);
				}
			}
		}

		.coupon-content {
			.preview {
				padding-top: 10rpx;
				display: block;
			}
		}
	}

	.rui-coupon_expired {
		.coupon-default {
			padding: 30rpx 0;
			display: flex;
			background-color: #fff;
		}

		.coupon-limit {
			width: 200rpx;
			flex-shrink: 0;
			text-align: center;
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;
			padding: 0 20rpx;

			.coupon-price {
				color: #9c9c9c !important;
			}
		}
		.row-right {
			background-color: #9c9c9c !important;
		}
	}
</style>