<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view v-else-if="taskInfo" class="main-content">
      <!-- 顶部状态栏 -->
      <view class="status-bar" :class="statusClass">
        <view class="status-content">
          <view class="status-left">
            <text class="status-text">{{ statusText }}</text>
            <text class="status-subtitle">{{ statusSubtitle }}</text>
          </view>
          <view class="reward-section">
            <text class="reward-points">{{ rewardPoints }}</text>
            <text class="reward-label">积分</text>
          </view>
        </view>
      </view>

      <!-- 任务内容区 -->
      <view class="content-area">
        <!-- 任务标题 -->
        <view class="title-section">
          <text class="task-title">{{ taskInfo.title }}</text>
          <view class="meta-info">
            <view class="difficulty-tag">
              <text class="difficulty-text">难度</text>
              <view class="stars">
                <text class="star" v-for="(item, index) in difficultyStars" :key="index">★</text>
                <text class="star-empty" v-for="(item) in emptyStarsWithKey" :key="item.key">☆</text>
              </view>
            </view>
            <view class="publisher-info">
              <!-- 后端应提供 publisherName，这里假设它在 taskInfo 中 -->
              <text class="publisher-text">发布者: {{ taskInfo.publisherName || '平台' }}</text>
            </view>
          </view>
        </view>

        <!-- 任务进度 -->
        <view class="progress-section" v-if="showProgress">
          <view class="progress-header">
            <text class="progress-title">参与进度</text>
            <text class="progress-count">{{ progressText }}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-inner" :style="{ width: progressPercent + '%' }"></view>
          </view>
          <view class="progress-info">
            <text class="progress-percent">{{ progressPercent }}%</text>
            <text class="remaining-spots" v-if="remainingSpots > 0">还剩{{ remainingSpots }}个名额</text>
            <text class="full-spots" v-else-if="taskInfo.maxParticipants > 0">已满员</text>
          </view>
        </view>

        <!-- 时间信息 -->
        <view class="time-section">
          <view class="time-block">
            <view class="time-icon">📅</view>
            <view class="time-content">
              <text class="time-label">开始时间</text>
              <text class="time-value">{{ taskInfo.startTime }}</text>
            </view>
          </view>
          <view class="time-block">
            <view class="time-icon">⏰</view>
            <view class="time-content">
              <text class="time-label">结束时间</text>
              <text class="time-value">{{ taskInfo.endTime }}</text>
            </view>
          </view>
        </view>

        <!-- 任务描述 -->
        <view class="description-section">
          <text class="section-title">任务说明</text>
          <text class="description-text">{{ taskInfo.description || '暂无详细说明' }}</text>
        </view>

        <!-- 任务要求 (假设后端可能返回 requirements 字段) -->
        <view class="requirements-section" v-if="taskInfo.requirements">
          <text class="section-title">任务要求</text>
          <text class="requirements-text">{{ taskInfo.requirements }}</text>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <view class="error-icon">❌</view>
      <text class="error-text">任务信息加载失败</text>
      <button class="retry-button" @click="retryLoad">重新加载</button>
    </view>

    <!-- 底部操作栏 -->
    <view class="action-bar" v-if="taskInfo && !loading">
      <button class="action-button"
        :class="btnClass"
        @click="handleTaskAction"
        :disabled="!canAcceptTask">
        {{ btnText }}
      </button>
    </view>

  </view>
</template>

<script>
// 假设你的 API 文件路径是 @/api/common.js
import { getTask, acceptTask } from '@/api/common.js'
// import { getToken } from '@/utils/auth' // 如果需要手动检查登录状态

export default {
  data() {
    return {
      taskInfo: null,
      loading: true,
      taskId: null,

      // 状态映射配置 (status 字段为数字类型)
      statusConfig: {
        1: {
          class: 'status-active',
          text: '进行中',
          subtitle: '任务正在火热进行中',
          btnClass: 'btn-primary'
        },
        2: {
          class: 'status-paused',
          text: '已暂停',
          subtitle: '任务暂时停止，请关注后续通知',
          btnClass: 'btn-disabled'
        },
        3: {
          class: 'status-completed',
          text: '已完成',
          subtitle: '任务已圆满结束',
          btnClass: 'btn-completed'
        },
        4: {
          class: 'status-cancelled',
          text: '已取消',
          subtitle: '任务已被取消',
          btnClass: 'btn-disabled'
        }
      }
    }
  },

  filters: {
    formatDate(value) {
      if (!value) return '待定';
      // 简单格式化，后端返回的可能是 'YYYY-MM-DD HH:mm:ss'
      // 你可能需要更复杂的日期格式化库如 moment.js 或 day.js，或者自己实现
      // 这里仅做基础展示，可能需要调整以匹配后端时间格式
      try {
        const date = new Date(value.replace(/-/g, "/")); // 兼容iOS
        if (isNaN(date.getTime())) return value; // 如果转换失败，返回原始值

        const y = date.getFullYear();
        const m = ('0' + (date.getMonth() + 1)).slice(-2);
        const d = ('0' + date.getDate()).slice(-2);
        const h = ('0' + date.getHours()).slice(-2);
        const min = ('0' + date.getMinutes()).slice(-2);
        return `${y}-${m}-${d} ${h}:${min}`;
      } catch (e) {
        return value; // 出错则返回原始值
      }
    }
  },

  computed: {
    rewardPoints() {
      return this.taskInfo?.rewardPoints || 0
    },

    difficultyStars() {
      const level = this.taskInfo?.difficultyLevel || 0
      const count = Math.min(Math.max(level, 0), 5)
      return Array.from({ length: count }, (_, i) => i)
    },

    emptyStarsWithKey() {
      const filledCount = this.taskInfo?.difficultyLevel || 0
      const filled = Math.min(Math.max(filledCount, 0), 5);
      const emptyCount = Math.max(5 - filled, 0);
      return Array.from({ length: emptyCount }, (_, i) => ({ key: `empty-${i}` }));
    },

    showProgress() {
      return this.taskInfo && Number(this.taskInfo.maxParticipants) > 0
    },

    progressText() {
      if (!this.taskInfo) return '0/0'
      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0
      return `${current}/${max}`
    },

    progressPercent() {
      if (!this.taskInfo) return 0
      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0
      if (max === 0) return current > 0 ? 100 : 0; // 如果最大是0，但已有参与者，可视为100% (或根据业务调整)
      const percent = Math.min(Math.max(Math.round((current / max) * 100), 0), 100)
      return percent
    },

    remainingSpots() {
      if (!this.taskInfo || !this.taskInfo.maxParticipants) return 0
      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0
      if (max === 0) return Infinity; // 如果最大是0，代表名额无限
      return Math.max(max - current, 0)
    },

    currentStatusConfig() {
      const status = this.taskInfo?.status || 1
      return this.statusConfig[status] || this.statusConfig[1]
    },

    statusClass() {
      return this.currentStatusConfig.class
    },

    statusText() {
      return this.currentStatusConfig.text
    },

    statusSubtitle() {
      return this.currentStatusConfig.subtitle
    },

    btnClass() {
        if (this.taskInfo && this.taskInfo.status === 1) {
            const current = Number(this.taskInfo.currentParticipants) || 0;
            const max = Number(this.taskInfo.maxParticipants) || 0;
            if (max > 0 && current >= max) { // 进行中但已满员
                return this.statusConfig[1].btnClass; // 保持主要按钮样式，但会被禁用
            }
        }
        return this.currentStatusConfig.btnClass;
    },

    btnText() {
      if (!this.taskInfo) return '加载中...'

      const status = this.taskInfo.status
      if (status !== 1) { // 非进行中状态
        return this.currentStatusConfig.text
      }

      // 进行中状态 (status === 1)
      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0

      if (max > 0 && current >= max) {
        return '已满员'
      }

      // 这里可以加入判断用户是否已接取此任务的逻辑
      // if (this.taskInfo.userHasAccepted) {
      //   return '已接取 (查看进度)' // 或其他文本
      // }

      return '立即接取'
    },

    canAcceptTask() {
      if (!this.taskInfo) return false

      // if (this.taskInfo.userHasAccepted) { // 如果用户已接取，则不能再次接取
      //  return false;
      // }

      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0
      const isActive = this.taskInfo.status === 1 // 任务状态为进行中

      // 检查任务是否已过结束时间
      let isExpired = false;
      if (this.taskInfo.endTime) {
        try {
            const endTimeDate = new Date(this.taskInfo.endTime.replace(/-/g, "/"));
            if (!isNaN(endTimeDate.getTime()) && new Date() > endTimeDate) {
                isExpired = true;
            }
        } catch (e) {
            // console.warn("解析结束时间失败:", this.taskInfo.endTime);
        }
      }

      return isActive && !isExpired && (max === 0 || current < max)
    }
  },

  onLoad(options) {
    this.taskId = options.taskId
    if (this.taskId) {
      this.getTaskDetail(this.taskId)
    } else {
      this.taskInfo = null;
      this.loading = false;
      this.showError('缺少任务ID参数')
    }
  },

  onPullDownRefresh() {
    if (this.taskId) {
        this.getTaskDetail(this.taskId).finally(() => {
            uni.stopPullDownRefresh();
        });
    } else {
        uni.stopPullDownRefresh(); // 如果没有taskId，也需要停止
    }
  },

  methods: {
    async getTaskDetail(taskId) {
      if (!taskId) return
      this.loading = true
      try {
        const res = await getTask(taskId) // API 调用
        // 假设后端成功时 res.code 为 0 或 200，数据在 res.data
        if (res && (res.code === 0 || res.code === 200) && res.data) {
          this.taskInfo = this.processTaskData(res.data)
          // console.log('获取任务详情成功:', this.taskInfo)
        } else {
          this.taskInfo = null;
          throw new Error(res.msg || '未能获取到任务数据')
        }
      } catch (error) {
        console.error('获取任务详情失败:', error)
        this.taskInfo = null;
        this.showError(error.message || '获取任务详情失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    processTaskData(data) {
      return {
        ...data,
        currentParticipants: Number(data.currentParticipants) || 0,
        maxParticipants: Number(data.maxParticipants) || 0,
        difficultyLevel: Math.min(Math.max(Number(data.difficultyLevel) || 1, 1), 5),
        rewardPoints: Number(data.rewardPoints) || 0,
        status: Number(data.status) || 1, // 确保 status 是数字
        // publisherName: data.publisherName || '平台管理员', // 如果后端不直接给，可以设置默认
        // userHasAccepted: data.userHasAccepted || false, // 后端应告知当前用户是否已接取
      }
    },

    async handleTaskAction() {
      if (!this.validateTaskForAction()) return

      if (!this.taskInfo || !this.taskInfo.id) {
        this.showError('无法操作：任务信息不完整')
        return
      }

      // 可选: 检查登录状态
      // const token = getToken();
      // if (!token) {
      //   this.showError('请先登录');
      //   // uni.navigateTo({ url: '/pages/login/login' }); // 跳转登录
      //   return;
      // }

      uni.showLoading({
        title: '正在处理...'
      })

      const payload = { id: this.taskInfo.id }
      const res = await acceptTask(payload) // 调用接取任务API
      
      // 假设后端成功时 res.code 为 0 或 200
      if (res && res.code === 200) {
        uni.hideLoading()
        uni.showToast({
          title: res.msg || '任务接取成功！',
          icon: 'success',
          duration: 1500
        })
        // 刷新任务数据
        setTimeout(() => {
          this.getTaskDetail(this.taskInfo.id)
        }, 300) // 短暂延迟以确保后端数据更新
      } else {
        uni.hideLoading()
        this.showError(res.msg || '操作失败，请稍后再试')
      }
    },

    validateTaskForAction() {
      if (!this.taskInfo) {
          this.showError('任务信息尚未加载')
          return false
      }
      if (this.taskInfo.status !== 1) {
        this.showError(`任务当前为"${this.statusText}"状态，无法接取`)
        return false
      }

      const current = Number(this.taskInfo.currentParticipants) || 0
      const max = Number(this.taskInfo.maxParticipants) || 0
      if (max > 0 && current >= max) {
        this.showError('抱歉，任务参与人数已满')
        return false
      }

      if (this.taskInfo.endTime) {
        try {
            const endTimeDate = new Date(this.taskInfo.endTime.replace(/-/g, "/"));
            if (!isNaN(endTimeDate.getTime()) && new Date() > endTimeDate) {
                this.showError('任务已过结束时间，无法接取');
                return false;
            }
        } catch (e) {
            // console.warn("解析结束时间失败:", this.taskInfo.endTime);
        }
      }
      // if (this.taskInfo.userHasAccepted) {
      //   this.showError('您已经接取过此任务了');
      //   return false;
      // }
      return true
    },

    showError(message) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
    },

    retryLoad() {
      if (this.taskId) {
        this.getTaskDetail(this.taskId)
      } else {
        this.showError('无法重试：缺少任务ID')
      }
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx); // 适配iPhone X等底部安全区域
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0; // 更柔和的边框色
  border-top: 4rpx solid #ff6b35; // 主题色
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #757575; // 更深一点的灰色
  font-size: 28rpx;
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #e57373; // 柔和的红色
}

.error-text {
  color: #757575;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-button {
  background: #ff6b35;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  transition: background-color 0.2s;
  &:active {
    background-color: #e65c2e;
  }
}

// 主要内容
.main-content {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

// 状态栏
.status-bar {
  padding: 50rpx 32rpx 40rpx;
  color: white;
  position: relative;
  overflow: hidden; // 确保伪元素不溢出

  &::before { // 添加一层半透明叠加，增强质感
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit; // 继承父元素的背景
    opacity: 0.1; // 轻微的叠加效果
    z-index: 0;
  }

  &.status-active {
    background: linear-gradient(135deg, #ff6b35, #ff9f43);
  }

  &.status-paused {
    background: linear-gradient(135deg, #ffc107, #ff9800); // 黄色系
  }

  &.status-completed {
    background: linear-gradient(135deg, #4CAF50, #66bb6a); // 绿色系
  }

  &.status-cancelled {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d); // 灰色系
  }
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative; // 确保内容在伪元素之上
  z-index: 1;
}

.status-left {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.status-subtitle {
  font-size: 24rpx;
  opacity: 0.85; // 稍高一点透明度
}

.reward-section {
  text-align: right;
}

.reward-points {
  font-size: 48rpx;
  font-weight: 800;
  display: block;
  line-height: 1;
}

.reward-label {
  font-size: 24rpx;
  opacity: 0.85;
  margin-top: 4rpx;
}

// 内容区域
.content-area {
  margin: -30rpx 24rpx 0; // 向上偏移与状态栏重叠一部分
  padding: 40rpx 32rpx;
  background: white;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  animation: slideUp 0.6s ease-out 0.2s both; // 延迟动画，配合fadeIn
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(40rpx); }
  to { opacity: 1; transform: translateY(0); }
}

// 标题区域
.title-section {
  margin-bottom: 40rpx;
}

.task-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #2c3e50; // 深蓝灰色
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.3;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap; // 允许换行
  gap: 16rpx; // 元素间距
}

.difficulty-tag {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #ff6b35, #ff9f43);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  color: white;
  font-size: 24rpx; // 统一字体大小
}

.difficulty-text {
  // font-size: 24rpx; // 已移至父元素
  font-weight: 500;
  margin-right: 10rpx;
}

.stars {
  display: flex;
  gap: 2rpx; // 星星间距
}

.star, .star-empty {
  font-size: 24rpx; // 确保星星大小一致
}

.star {
  color: #fff;
  text-shadow: 0 0 4rpx rgba(255, 255, 255, 0.5); // 星星发光效果
}

.star-empty {
  color: rgba(255, 255, 255, 0.4); // 未点亮星星颜色
}

.publisher-info {
  font-size: 24rpx;
  color: #757575;
}

.publisher-text {
  // 样式已在父元素定义
}


// 进度区域
.progress-section {
  margin-bottom: 40rpx;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef); // 浅灰色渐变
  border-radius: 24rpx;
  border: 1rpx solid #e0e0e0; // 细边框
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 30rpx;
  color: #2c3e50;
  font-weight: 600;
}

.progress-count {
  font-size: 26rpx;
  color: #ff6b35; // 主题色
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 10rpx;
  background: #ecf0f1; // 进度条背景色
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  position: relative;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #ff6b35, #ff9f43); // 主题色渐变
  border-radius: 5rpx;
  position: relative;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1); // 平滑过渡

  &::after { // 添加光泽效果
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    animation: shimmer 2s infinite linear; // 流光动画
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-20deg); }
  100% { transform: translateX(100%) skewX(-20deg); }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-percent {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: 600;
}

.remaining-spots {
  font-size: 24rpx;
  color: #27ae60; // 绿色表示剩余
  font-weight: 500;
}

.full-spots {
  font-size: 24rpx;
  color: #e74c3c; // 红色表示已满
  font-weight: 500;
}

// 时间区域
.time-section {
  display: flex;
  gap: 20rpx; // 块间距
  margin-bottom: 40rpx;
}

.time-block {
  flex: 1;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx; // 图标与内容间距
  border: 1rpx solid #e0e0e0;
}

.time-icon {
  font-size: 32rpx; // 图标大小
  color: #ff6b35; // 图标颜色
}

.time-content {
  flex: 1;
}

.time-label {
  font-size: 24rpx;
  color: #95a5a6; // 标签颜色
  margin-bottom: 6rpx;
  display: block;
}

.time-value {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 600;
}

// 描述与要求区域
.description-section, .requirements-section {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #e0e0e0;
}

.section-title {
  font-size: 30rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #ffdac8; // 标题下划线，用主题色的浅色
}

.description-text, .requirements-text {
  font-size: 28rpx;
  color: #555; // 文本颜色
  line-height: 1.7; // 更舒适的行高
  white-space: pre-wrap; // 保留换行和空格
}

// 底部操作栏
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx; // 调整内边距
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); // 适配底部安全区
  background: rgba(255, 255, 255, 0.97); // 更高的不透明度，增强毛玻璃效果
  backdrop-filter: blur(15rpx); // 更强的模糊
  box-shadow: 0 -6rpx 24rpx rgba(0, 0, 0, 0.08); // 更柔和的阴影
  z-index: 100;
}

.action-button {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  border-radius: 48rpx; // 全圆角
  font-size: 34rpx;
  font-weight: 600;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  // 点击时的光效
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300%; // 足够大以覆盖按钮
    height: 300%;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    // transition: transform 0.5s, opacity 0.7s; // 移除这里的transition，由active状态控制
  }

  &:active::before {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    transition: transform 0.5s, opacity 0s; // 激活时立即显示，然后扩展
  }


  &.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #ff9f43);
    color: white;
    box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.35);

    &:active {
      transform: translateY(2rpx) scale(0.98); // 轻微下沉和缩小
      box-shadow: 0 4rpx 10rpx rgba(255, 107, 53, 0.3);
    }
    &[disabled] { // 禁用时的样式
        background: linear-gradient(135deg, #fcd9cc, #ffe6cf); // 主题色的浅色，保持色调
        color: #ffffff;
        opacity: 0.7;
        box-shadow: none;
    }
  }

  &.btn-completed {
    background: linear-gradient(135deg, #4CAF50, #66bb6a);
    color: white;
    box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
    &[disabled] { // 已完成状态通常是禁用的
        opacity: 0.8;
        box-shadow: none;
    }
  }

  &.btn-disabled { // 通用禁用样式
    background: #e0e0e0; // 更中性的灰色
    color: #a0a0a0;
    box-shadow: none;
    // 对于禁用的按钮，移除点击效果
    &::before {
      display: none;
    }
    &:active::before {
      display: none;
    }
  }
}
</style>