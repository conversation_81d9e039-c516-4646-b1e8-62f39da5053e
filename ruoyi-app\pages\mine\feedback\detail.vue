<template>
	<view class="app-container">
		<z-paging ref="paging" v-model="list" use-page-scroll @query="getList">
			<template #top>
				<rui-nav-bar title="投诉记录" back></rui-nav-bar>
			</template>
			<view class="list-wrap">
				<view class="list-item" v-for="(item,index) in list" :key="index">
					<view class="list-item_head">
						<text class="time" style="color: #000;">{{item.createTime}}</text>
						<text style="color: #F55726;" v-if="item.status==='0'">待处理</text>
						<text style="color: #40AE36;" v-else>已完成</text>
					</view>
					<view class="list-preview">
						<view class="preview-item">
							<text>姓名</text>
							<text>{{item.name}}</text>
						</view>
						<view class="preview-item">
							<text>手机号</text>
							<text>{{item.phone}}</text>
						</view>
						<view class="preview-item">
							<text>投诉类型</text>
							<text>{{item.type}}</text>
						</view>
						<view class="preview-item">
							<text>投诉内容</text>
							<text>{{item.content||"无"}}</text>
						</view>
						<view class="preview-item">
							<text>投诉附件</text>
							<text style="color: #1890ff;" @click="handlePreview(item.images)">查看图片</text>
						</view>
						<block v-if="item.status!=='0'">
							<view class="preview-item line">
								<text>反馈时间</text>
								<text>{{item.updateTime}}</text>
							</view>
							<view class="preview-item">
								<text>反馈内容</text>
								<text>{{item.feedbackContent||"无"}}</text>
							</view>
							<view class="preview-item">
								<text>反馈附件</text>
								<text style="color: #1890ff;" @click="handlePreview(item.feedbackImages)">查看图片</text>
							</view>
						</block>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { listComplain } from '@/api/common.js'
	export default {
		data() {
			return {
				list: []
			};
		},
		methods: {
			handlePreview(images) {
				if (!images) {
					uni.showToast({
						title: '没有图片文件',
						icon: 'none'
					})
					return
				}
				const urls = images.split(',').map(url => {
					return this.format_url(url)
				})
				uni.previewImage({
					urls: urls
				})
			},
			format_url(url) {
				const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
				const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
				return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
			},
			getList(pageNo, pageSize) {
				listComplain({ pageNum: pageNo, pageSize }).then(res => {
					this.$refs.paging.complete(res.rows.map(item => {
						item.open = false
						return item
					}))
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #f7f8fa 36%);
	}

	.list-item {
		background-color: #fff;
		margin: 28rpx 28rpx;
		padding: 24rpx 28rpx 10rpx;
		color: #333333;
		border-radius: 28rpx;
		position: relative;
		overflow: hidden;
		// box-shadow: 0px -3rpx 6rpx rgba(173, 174, 179, 0.09), 0px 3rpx 6px rgba(173, 174, 179, 0.09);
		// background: linear-gradient(170deg, rgba(252, 234, 212, 0.6) 0%, #fff 34%);


		.list-item_head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 28rpx;
			border-bottom: 1px solid rgba(236, 236, 236, 1);
			font-size: 24rpx;

			text:nth-child(1) {
				font-weight: 400;
				color: rgba(153, 153, 153, 1);
			}
		}

		.line {
			width: 100%;
			border-top: 2rpx dashed rgba(236, 236, 236, 1);
		}

		.list-preview {
			padding-top: 10rpx;

			.preview-item {
				padding: 16rpx 0;
				display: flex;
				justify-content: space-between;

				text:nth-child(1) {
					font-size: 24rpx;
					color: #323233;
					flex-shrink: 0;
				}

				text:nth-child(2) {
					padding-left: 70rpx;
					font-size: 24rpx;
					color: #646566;
					text-align: right;
				}
			}
		}
	}
</style>