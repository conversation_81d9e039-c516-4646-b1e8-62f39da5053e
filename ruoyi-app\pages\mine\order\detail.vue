<template>
	<view class="container">
		<image class="topbg" src="../../../static/images/topbg.png" mode="widthFix"></image>
		<rui-nav-bar back color="#fff"></rui-nav-bar>
		<main>
			<view class="main-head">
				<block v-if="form.status==='0'">
					<view>
						<image src="../../../static/images/icon/status_0.png" mode=""></image>
						<text>待兑换</text>
					</view>
					<view>已付款，待兑换</view>
				</block>
				<block v-if="form.status==='1'">
					<view>
						<text>已完成</text>
					</view>
					<view>核销时间：{{form.updateTime}}</view>
				</block>
				<block v-if="form.status==='2'">
					<view>
						<text>已取消</text>
					</view>
					<view>取消时间：{{form.updateTime}}</view>
				</block>
			</view>
			<rui-card title="基本信息">
				<rui-describe label="订单编号">{{form.goodsId}}</rui-describe>
				<rui-describe label="订单价格"><text class="price">{{form.price}}</text><text style="margin-left: 6rpx;">积分</text>
				</rui-describe>
				<rui-describe label="物流单号" :content="form.logisticsNo || '暂无'"></rui-describe>
			</rui-card>
			<rui-card title="商品信息">
				<rui-describe label="商品编号" :content="form.goods.goodsId"></rui-describe>
				<rui-describe label="商品名称" :content="form.goods.goodsName"></rui-describe>
			</rui-card>
			<rui-card title="支付信息">
				<rui-describe label="商品数量" :content="form.goodsCount+'件'"></rui-describe>
				<rui-describe label="下单时间" :content="form.createTime"></rui-describe>
				<rui-describe label="到期时间" :content="endTime"></rui-describe>
			</rui-card>
			<rui-card title="核销信息" v-if="form.status==='1'">
				<rui-describe label="核销账号" :content="form.updateBy"></rui-describe>
				<rui-describe label="核销时间" :content="form.updateTime"></rui-describe>
			</rui-card>
		</main>
		<uni-popup ref="popup" type="center" border-radius="10px 10px 0 0">
			<view class="qr-popup-container">
				<view class="qr-popup-header">
					<text class="qr-popup-title">兑换码</text>
					<text class="qr-popup-close" @click="closePopup">×</text>
				</view>
				<view class="qr-popup-content">
					<view class="qr-canvas-wrapper">
						<canvas id="qrcode" canvas-id="qrcode" style="width: 250px;height: 250px;"></canvas>
					</view>
					<view class="qr-popup-info">
						<text class="qr-popup-tip">请向商家出示此二维码进行核销</text>
						<!-- <text class="qr-popup-orderid">订单号：{{form.orderId}}</text> -->
					</view>
				</view>
				<!-- <view class="qr-popup-footer">
					<button class="qr-popup-btn" @click="copyOrderId">复制订单号</button>
				</view> -->
			</view>
		</uni-popup>
		<rui-fixed-bottom>
			<rui-button  width="200rpx" @click="handleBack">返回</rui-button>
			<block v-if="!scene">
				<rui-button type="danger" width="200rpx" @click="handleCancel"
					v-if="form.status==='0'">取消订单</rui-button>
				<rui-button type="success" width="240rpx"  @click="handleCode"
					v-if="form.status==='0' && form.goods && form.goods.goodsType==='1'">出示兑换码</rui-button>
			</block>
			<block v-else>
				<rui-button type="success" width="400rpx;" @click="handleSubmit"
					v-if="form.status==='0'">确认核销</rui-button>
			</block>
		</rui-fixed-bottom>
		<zero-loading type="atom" mask v-if="loading" mask-opacity="0.6"></zero-loading>
	</view>
</template>

<script>
	import { getOrder, orderCancel, createQRCode ,orderComplete} from "@/api/common.js";
	import {mapGetters} from 'vuex'
	import UQRCode from 'uqrcodejs'; // npm install uqrcodejs
	export default {
		computed: {
			...mapGetters(["roles"]),
			endTime() {
				if (this.form.createTime) {
					return this.calculateEndTime(this.form.createTime, this.form.goods.effectiveDays)
				}
			},
			hasRole() {
				return this.roles.includes("ROLE_HX")
			},
		},
		data() {
			return {
				form: {},
				loading: true,
				qrCodeSrc: "",
				scene: false,
			};
		},
		onLoad(option) {
			if (option.scene) {
				this.scene = true
				const { orderId, s } = this.parsePathAndParams(option.scene)
				this.form.orderId = orderId
			} else {
				this.scene = false
				this.form.orderId = option.orderId
			}
			this.getOrder()
		},
		methods: {
			handleSubmit() {
				if(!this.hasRole){
					uni.showToast({
						title: '无权限，请联系管理员分配角色！',
						icon: 'none',
						duration:3000
					})
					return
				}
				uni.showModal({
					title: '提示',
					content: '确定核销吗？',
					success: (res) => {
						if (res.confirm) {
							orderComplete({ orderId: this.form.orderId}).then(res => {
								if (res.code === 200) {
									uni.showToast({
										title: '核销成功',
										icon: 'none'
									})
									this.getOrder()
								}
							})
						}
					}
				})
			},
			parsePathAndParams(scene) {
				let result = {};
				let decodedScene = decodeURIComponent(scene);
				while (decodedScene !== decodeURIComponent(decodedScene)) {
					decodedScene = decodeURIComponent(decodedScene);
				}
				decodedScene.split('&').forEach(param => {
					let [key, value] = param.split('=');
					result[key] = value;
				});
				return result;
			},
			handleCode() {
				this.loading = true
				 // 获取uQRCode实例
				  var qr = new UQRCode();
				  // 设置二维码内容
				  qr.data = this.form.orderId;
				  // 设置二维码大小，必须与canvas设置的宽高一致
				  qr.size = 250;
				  // 调用制作二维码方法
				  qr.make();
				  // 获取canvas上下文
				  var canvasContext = uni.createCanvasContext('qrcode', this); // 如果是组件，this必须传入
				  // 设置uQRCode实例的canvas上下文
				  qr.canvasContext = canvasContext;
				  // 调用绘制方法将二维码图案绘制到canvas上
				  qr.drawCanvas();
				  setTimeout(()=>{
					  this.$refs.popup.open()
					  this.loading = false
				  },3000)
			},
			handlePj() {
				this.$tab.navigateTo(`/pages/evaluate/release?orderId=${this.form.orderId}`)
			},
			handleBack() {
				uni.navigateBack({
					fail() {
						uni.reLaunch({
							url:'/pages/layout/index'
						})
					}
				})
			},
			handleCancel() {
				uni.showModal({
					title: '提示',
					content: '确定取消订单吗？',
					success: (res) => {
						if (res.confirm) {
							orderCancel({ orderId: this.form.orderId }).then(res => {
								if (res.code === 200) {
									uni.showToast({
										title: '取消成功',
										icon: 'none'
									})
									this.getOrder()
								} else {
									uni.showToast({
										title: res.msg,
										icon: "error"
									})
									this.getOrder()
								}
							})
						}
					}
				})
			},
			calculateEndTime(startTime, daysToAdd) {
				let date = new Date(startTime);
				date.setDate(date.getDate() + daysToAdd);
				const padZero = (num) => (num < 10 ? '0' : '') + num;
				let endYear = date.getFullYear();
				let endMonth = padZero(date.getMonth() + 1); // 月份从0开始，+1表示真实月份
				let endDay = padZero(date.getDate());
				let endHour = padZero(date.getHours());
				let endMinute = padZero(date.getMinutes());
				return `${endYear}.${endMonth}.${endDay} ${endHour}:${endMinute}:00`;
			},
			getOrder() {
				this.loading = true
				getOrder(this.form.orderId).then(res => {
					if (res.code === 200) {
						this.form = res.data
					}
				}).finally(() => {
					this.loading = false
				})
			},
			closePopup() {
				this.$refs.popup.close()
			},
			copyOrderId() {
				uni.setClipboardData({
					data: this.form.orderId,
					success: () => {
						uni.showToast({
							title: '订单号已复制',
							icon: 'success'
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: rgba(248, 249, 250, 1);
	}

	.container {
		.topbg {
			position: absolute;
			width: 100%;
			float: left;
			z-index: -1;
		}

		main {
			padding: 8% 30rpx 0;

			.main-head {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding-bottom: 50rpx;
				color: #fff;

				view:nth-child(1) {
					font-size: 34rpx;
					line-height: 60rpx;
					display: flex;
					align-items: center;

					image {
						width: 34rpx;
						height: 34rpx;
						margin-right: 10rpx;
					}
				}

				view:nth-child(2) {
					font-size: 24rpx;
					font-weight: 400;
					line-height: 34rpx;
				}
			}
		}
	}

	.price {
		color: red;
		font-size: 40rpx;
		font-weight: bold;
	}

	.rui-fixed-bottom {
		display: flex;
		justify-content: flex-end;
		padding-right: 28rpx;
		padding-top: 10rpx;

		.rui-button {
			margin-left: 20rpx;
		}
	}

	/* 二维码弹窗样式 */
	.qr-popup-container {
		background: #fff;
		border-radius: 24rpx;
		overflow: hidden;
		margin: 40rpx 20rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
		max-width: 600rpx;
	}

	.qr-popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 40rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
	}

	.qr-popup-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #fff;
	}

	.qr-popup-close {
		font-size: 56rpx;
		color: #fff;
		line-height: 1;
		padding: 8rpx;
		opacity: 0.8;
		
		&:active {
			opacity: 1;
		}
	}

	.qr-popup-content {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.qr-canvas-wrapper {
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 32rpx;
	}

	.qr-popup-info {
		text-align: center;
		margin-bottom: 32rpx;
	}

	.qr-popup-tip {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 16rpx;
		display: block;
	}

	.qr-popup-orderid {
		font-size: 24rpx;
		color: #666;
		background: #f8f9fa;
		padding: 12rpx 24rpx;
		border-radius: 20rpx;
		display: inline-block;
	}

	.qr-popup-footer {
		padding: 0 40rpx 40rpx;
	}

	.qr-popup-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}
	}
</style>