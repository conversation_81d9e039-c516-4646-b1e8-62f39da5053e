<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList">
			<template #top>
				<rui-nav-bar title="积分记录" back></rui-nav-bar>
				<!-- <rui-vtab v-model="currentTab" bg="#fff" :tabs="tabs" overspread @change="$refs.paging.reload"></rui-vtab> -->
			</template>
			<view class="list-wrap">
				<view class="list-item" v-for="(item,index) in list" :key="index">
		<!-- 			<view class="list-item_head">
						<text class="time" style="color: #000;">{{item.createTime}}</text>
						<text style="color: #E0AC7D;" v-if="item.status==='0'">待处理</text>
						<text style="color: #40AE36;" v-if="item.status==='1'">审核通过</text>
						<text style="color: #F55726;" v-if="item.status==='2'">不通过</text>
					</view> -->
					<view class="list-preview">
						<view class="preview-item">
							<text>店铺名称</text>
							<text>{{item.storeName}}</text>
						</view>
						<view class="preview-item">
							<text>消费金额</text>
							<text>{{item.totalAmount}}</text>
						</view>
						<view class="preview-item">
							<text>消费时间</text>
							<text>{{item.dissipateTime}}</text>
						</view>
						<view class="preview-item">
							<text>票据图片</text>
							<text style="color: #1890ff;" @click="handlePreview(item.image)">查看图片</text>
						</view>
						<view class="preview-item">
							<text>积分数量</text>
							<text style="color: #F55726;font-size: 32rpx;">{{item.integral || '0'}}积分</text>
						</view>
						<view class="preview-item" v-if="item.status!=='0'">
							<text>审核时间</text>
							<text>{{item.updateTime}}</text>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { listIntegral } from "@/api/common.js";
	export default {
		computed: {
			status() {
				if (this.currentTab == '-1') {
					return ""
				}
				return this.currentTab
			}
		},
		data() {
			return {
				tabs: [
					{ label: "全部", value: "-1" },
					{ label: "待审核", value: "0" },
					{ label: "审核通过", value: "1" },
					{ label: "审核失败", value: "2" },
				],
				currentTab: "",
				list: [],
			}
		},
		onLoad({ status }) {
			this.currentTab = status || "-1"
			console.log(this.currentTab);
		},
		methods: {
			handlePreview(images) {
				if (!images) {
					uni.showToast({
						title: '没有图片',
						icon: 'none'
					})
					return
				}
				const urls = images.split(',').map(url => {
					return this.format_url(url)
				})
				uni.previewImage({
					urls: urls
				})
			},
			format_url(url) {
				const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
				const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
				return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
			},
			getList(pageNo, pageSize) {
				listIntegral({ pageNum: pageNo, pageSize, status: this.status }).then(res => {
					this.$refs.paging.complete(res.rows);
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		// background: #fff;
			background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #f7f8fa 36%);
	}

	.list-item {
		background-color: #fff;
		margin: 28rpx 28rpx;
		padding: 24rpx 28rpx 10rpx;
		color: #333333;
		border-radius: 28rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0px -3rpx 6rpx rgba(173, 174, 179, 0.09), 0px 3rpx 6px rgba(173, 174, 179, 0.09);
		// background: linear-gradient(170deg, rgba(252, 234, 212, 0.6) 0%, #fff 34%);


		.list-item_head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 28rpx;
			border-bottom: 1px solid rgba(236, 236, 236, 1);
			font-size: 24rpx;

			text:nth-child(1) {
				font-weight: 400;
				color: rgba(153, 153, 153, 1);
			}
		}

		.line {
			width: 100%;
			border-top: 2rpx dashed rgba(236, 236, 236, 1);
		}

		.list-preview {
			padding-top: 10rpx;

			.preview-item {
				padding: 16rpx 0;
				display: flex;
				justify-content: space-between;

				text:nth-child(1) {
					font-size: 24rpx;
					color: #323233;
					flex-shrink: 0;
				}

				text:nth-child(2) {
					padding-left: 70rpx;
					font-size: 24rpx;
					color: #646566;
					text-align: right;
				}
			}
		}
	}
</style>