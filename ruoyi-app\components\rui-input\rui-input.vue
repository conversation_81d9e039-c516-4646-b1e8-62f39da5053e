<template>
	<view class="rui-input">
		<block v-if="type==='eye'">
			<block v-if="showPsw">
				<input v-model="_value" placeholder-class="rui-input_placeholder" type="text" @blur="blur" @focus="focus"
					:placeholder="placeholder" :disabled="disabled" />
				<rui-icon name="eye" @click="handleEye"></rui-icon>
			</block>
			<block v-else>
				<input v-model="_value" placeholder-class="rui-input_placeholder" type="password" @blur="blur" @focus="focus"
					:placeholder="placeholder" :disabled="disabled" />
				<rui-icon name="eye-close" @click="handleEye"></rui-icon>
			</block>
		</block>
	<block v-else>
		<input v-model="_value" placeholder-class="rui-input_placeholder" :type="type" @blur="blur" @focus="focus"
			:placeholder="placeholder" :disabled="disabled" />
	</block>
	</view>
</template>

<script>
	export default {
		name: "rui-input",
		emits: ['input', 'focus', 'blur'],
		props: {
			value: {
				type: String,
				default: ''
			},
			type: {
				type: String,
				default: 'text'
			},
			placeholder: {
				type: String,
				default: '请输入内容'
			},
			align:{
				type: String,
				default: 'inherit'
			},
			disabled: {
				type: Boolean,
				default: false
			},
			clearable: {
				type: Boolean,
				default: false
			},
			border: {
				type: Boolean,
				default: false
			},
		
		},
		computed: {
			_value: {
				get() { return this.value },
				set(val) { this.$emit('input', val) }
			},
		},
		data() {
			return {
				showPsw: false,
			};
		},
		methods: {
			blur(e) { this.$emit('blur', e) },
			focus(e) { this.$emit('focus', e) },
			handleEye(e) {
				this.showPsw = !this.showPsw
			}
		},
	}
</script>

<style lang="scss">
	.rui-input {
		width: 100%;
		padding: 28rpx 38rpx;
		box-sizing: border-box;
		font-size: 14px;
		outline: none;
		background-color: #F8F8F8;
		border-radius: 80rpx;
		display: flex;
		align-items: center;
		margin-bottom: 28rpx;
		input {
			flex: 1;
		}

		.rui-input_disabled {
			pointer-events: none;
		}

		.rui-input_placeholder {
			font-size: 24rpx;
			color: #c8c9cc;
		}
	}
</style>