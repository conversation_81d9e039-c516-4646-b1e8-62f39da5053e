@font-face {
  font-family: "iconfont"; /* Project id 4561728 */
  src: url('//at.alicdn.com/t/c/font_4561728_j1oynx5z4um.woff2?t=1717143428488') format('woff2'),
       url('//at.alicdn.com/t/c/font_4561728_j1oynx5z4um.woff?t=1717143428488') format('woff'),
       url('//at.alicdn.com/t/c/font_4561728_j1oynx5z4um.ttf?t=1717143428488') format('truetype');
}
.rui-icon {
  font-family: "iconfont";
  font-size: 32rpx;
  display: inline-block;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
	color: #777777;
}

.icon-scan:before {
  content: "\e7d4";
}

.icon-bell:before {
  content: "\e7c4";
}

.icon-bell-fill:before {
  content: "\e866";
}

.icon-delete:before {
  content: "\e7c3";
}

.icon-delete-fill:before {
  content: "\e863";
}

.icon-poweroff:before {
  content: "\e78c";
}

.icon-like:before {
  content: "\e7c8";
}

.icon-unlike:before {
  content: "\e7c9";
}

.icon-unlock:before {
  content: "\e7ca";
}

.icon-verticalleft:before {
  content: "\e7ea";
}

.icon-right:before {
  content: "\e7eb";
}

.icon-left:before {
  content: "\e7ec";
}

.icon-up:before {
  content: "\e7ed";
}

.icon-down:before {
  content: "\e7ee";
}

.icon-arrowright:before {
  content: "\e7ef";
}

.icon-arrowup:before {
  content: "\e7f0";
}

.icon-arrowleft:before {
  content: "\e7f1";
}

.icon-arrowdown:before {
  content: "\e7f2";
}

.icon-menu:before {
  content: "\e7f4";
}

.icon-check:before {
  content: "\e7fc";
}

.icon-close:before {
  content: "\e7fd";
}

.icon-eye-fill:before {
  content: "\e869";
}

.icon-like-fill:before {
  content: "\e86a";
}

.icon-unlike-fill:before {
  content: "\e86b";
}

.icon-star-fill:before {
  content: "\e86c";
}

.icon-setting:before {
  content: "\e78e";
}

.icon-eye:before {
  content: "\e78f";
}

.icon-setting-fill:before {
  content: "\e871";
}

.icon-eye-close:before {
  content: "\e8ff";
}
