<template>
	<view class="container">
		<!-- 摄像头全屏显示 -->
		<camera v-if="status===1" class="camera" device-position="back" flash="auto" @error="onCameraError" ref="camera">
		</camera>
		<image class="phone-image" v-if="photo&&status===2" :src="photo" mode="widthFix"></cover-image>
		<cover-view class="camera-tool" v-if="status===1">
			<cover-view class="icon-button" @click="handleBack">
				<cover-image src="@/static/images/icon/undo-filled.png"></cover-image>
				<cover-view>返回</cover-view>
			</cover-view>
			<cover-view class="icon-button" @click="handleCompress">
				<cover-image src="@/static/images/icon/image.png"></cover-image>
				<cover-view>相册选取</cover-view>
			</cover-view>
		</cover-view>
		<cover-view class="camera-button" v-if="status===2">
			<cover-view class="reset-button" @click="handleReset">重新拍照</cover-view>
			<cover-view class="upload-button" @click="handleUpload">上传图片</cover-view>
		</cover-view>
		<cover-view class="icon-center" @click="takePhoto" v-if="status===1">
			<cover-image src="@/static/images/icon/camera-filled.png"></cover-image>
		</cover-view>
	</view>
</template>

<script>
	import { getToken } from '@/utils/auth'
	export default {
		data() {
			return {
				photo: null,
				status: 1, // 1摄像头 2拍照
			};
		},
		methods: {
			// 拍照
			takePhoto() {
				const cameraContext = uni.createCameraContext();
				cameraContext.takePhoto({
					quality: 'high',
					success: (res) => {
						this.status = 2
						this.photo = res.tempImagePath;
					},
					fail: (error) => {
						uni.showToast({ title: '拍照失败', icon: 'none' });
						console.error(error);
					}
				});
			},
			handleReset() {
				this.photo = null
				this.status = 1
			},
			// 上传照片
			handleUpload() {
				if (this.photo) {
					uni.showLoading({ title: '上传中', mask: true });
					uni.uploadFile({
						url: process.env.VUE_APP_BASE_API + "/system/IntegralReceipt/upload", // 上传的服务器地址
						filePath: this.photo,
						name: 'file',
						header: {
							"Content-Type": "application/json",
							'Authorization': 'Bearer ' + getToken(),
						},
						success: (uploadFileRes) => {
							const res = JSON.parse(uploadFileRes.data)
							uni.hideLoading()
							uni.showModal({
								title: "提示",
								content: res.msg,
								confirmText: "确定",
								showCancel: false,
								success: (res) => {
										this.handleReset()
								}
							})
						},
						fail: (error) => {
							uni.showToast({ title: '上传失败', icon: 'none' });
						}
					});
				}
			},
			// 选择相册
			handleCompress(tempFilePaths) {
				uni.chooseImage({
					count: 1, // 默认9
					sizeType: ['compressed'],
					sourceType: ['album'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						uni.compressImage({
							src: tempFilePaths[0],
							quality: 80,
							success: (res) => {
								this.photo = res.tempFilePath;
								this.status = 2
							}
						});
					}
				})
			},
			handleBack() {
				uni.navigateBack()
			},
			// 处理摄像头错误
			onCameraError(e) {
				// uni.showToast({ title: '摄像头打开失败', icon: 'error' });
				// setTimeout(()=>{
				// 	uni.navigateBack()
				// },1000)
			}
		}
	};
</script>

<style lang="scss">
	.container {
		position: relative;
		width: 100%;
		height: 100vh;
		background-color: #000;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}

	.camera {
		width: 100%;
		height: 100%;
	}

	.camera-tool {
		position: absolute;
		bottom: 80rpx;
		left: 40rpx;
		right: 40rpx;
		height: 130rpx;
		border: 1rpx solid #fff;
		background-color: rgba(255,255,255, 0.2);
		border-radius: 130rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 65rpx;
		overflow: visible;
		z-index: 99;

		.icon-button {
			width: 100rpx;
			height: 100rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #fff;
			transition: all .3s;

			&:active {
				transform: scale(.95);
			}

			cover-image {
				width: 58rpx;
				height: 58rpx;
			}

			cover-view {
				font-size: 24rpx;
				margin-top: 6rpx;
			}
		}


	}
		.icon-center {
			width: 150rpx;
			height: 150rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #000;
			border-radius: 50%;
			position: absolute;
			left: 50%;
			bottom: 120rpx;
			transform: translateX(-50%);
			z-index: 999;
			color: #fff;
			transition: all .3s;
			z-index: 999;

			&:active {
				transform: scale(.95) translateX(-50%);
			}

			cover-image {
				width: 80rpx;
				height: 80rpx;
			}
		}
	.phone-image {
		width: 100%;
		object-fit: contain;
	}

	.camera-button {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		bottom: 80rpx;
		height: 90rpx;
		display: flex;
		justify-content: center;
		z-index: 8;
		white-space: nowrap;
		overflow: hidden;

		cover-view {
			flex: 1;
			height: 100%;
			white-space: nowrap;
			border-radius: 80rpx;
			padding: 0 80rpx;
			line-height: 90rpx;
			margin: 0 20rpx;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			border: 1rpx solid #fff;
			box-sizing: border-box;
			transition: all .3s;

			&:active {
				opacity: .8;
			}
		}

		.reset-button {
			color: #fff;
		}

		.upload-button {
			background-color: #fff;
			color: #222222;
		}
	}
</style>