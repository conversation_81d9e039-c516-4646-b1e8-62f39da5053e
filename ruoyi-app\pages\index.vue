<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList" :fixed="false">
			<rui-nav-bar title="首页" align="center"></rui-nav-bar>
			<view class="search-wrap" @click="handleJfdl">
				<input v-model="searchText" placeholder="搜索积分商品" disabled/>
			</view>
			<Xsuu-swiper v-if="images.length" :swiperItems="images" style="margin-bottom: 50rpx;"></Xsuu-swiper>
<!-- 			<view class="nav-wrap">
				<view class="nav-row">
					<image src="../static/images/nav/拍照积分.png" mode="widthFix" @click="handlePzjf"></image>
					<image src="../static/images/nav/领券中心.png" mode="widthFix" @click="handleLqzx"></image>
				</view>
				<view class="nav-row" style="grid-template-columns: repeat(3, 1fr)">
					<image src="../static/images/nav/停车缴费.png" mode="widthFix" @click="handleTcjf"></image>
					<image src="../static/images/nav/加入社群.png" mode="widthFix" @click="handleJrsq"></image>
					<image src="../static/images/nav/积分兑礼.png" mode="widthFix" @click="handleJfdl"></image>
				</view>
			</view> -->

			<rui-subtitle title="积分兑礼" style="margin-top: 30rpx;">
				<view class="more-button" @click="handleMore">
					<text>查看更多</text>
					<uni-icons type="right" size="12"></uni-icons>
				</view>
			</rui-subtitle>

			<view class="list-wrap">
				<view class="card-box" v-for="(item,index) in list" :key="index" @click="handleDetail(item)">
					<rui-image-preview :preview="false" :src="item.goodsImage" height="360rpx"></rui-image-preview>
					<view class="card-content">
						<view class="card-content_title">{{item.goodsName}}</view>
						<view class="card-content_row">
							<view class="js">{{item.goodsIntegral}} <text style="font-size: 20rpx;">积分</text> </view>
							<rui-button type="primary" height="40rpx" size="22" @click="handleDetail(item)">立即兑换</rui-button>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import XsuuSwiper from "@/components/Xss-swiper/Xsuu-swiper.vue"
	import { goodsList, bannerList } from '@/api/common.js'
	import { getToken } from '@/utils/auth'
	export default {
		components: { XsuuSwiper },
		data() {
			return {
				currentTab: 0,
				tabs: [
					{ label: "全部", value: "" },
					{ label: "增值服务", value: "1" }
				],
				searchText: "",
				images: [],
				current: 0,
				list: []
			}
		},
		created() {
			this.getBanner()
		},
		methods: {
			getBanner() {
				bannerList().then(res => {
					this.images = res.rows.map(item => {
						item.img = "https://mall.hqidc.net/prod-api" + item.bannerImageUrl
						item.button = item.bannerLink ? 1 : 0
						return item
					})
				})
			},
			getList(pageNo, pageSize) {
				goodsList({ pageNum: pageNo, pageSize }).then(res => {
					// goodsList({ current: pageNo, size:pageSize }).then(res => {
					this.$refs.paging.complete(res.rows)
				})
			},
			checkLoginAndNavigate(url) {
				if (!getToken()) {
					this.$store.commit('app/toggleLoginModal', true);
					return false;
				}
				uni.navigateTo({ url });
				return true;
			},
			handleDetail(item) {
				this.checkLoginAndNavigate(`/pages/goods/detail?goodsId=${item.goodsId}`);
			},
			handleSearch() {
				// 搜索处理逻辑
			},
			handlePzjf() {
				this.checkLoginAndNavigate("/pages/integral/camera");
			},
			handleLqzx() {
				this.$store.commit('app/setTabbarIndex', 1);
			},
			handleTcjf() {
				this.checkLoginAndNavigate("/pages/parking/parking");
			},
			handleJrsq() {
				this.checkLoginAndNavigate("/pages/mine/group/group");
			},
			handleJfdl() {
				this.checkLoginAndNavigate("/pages/goods/goods");
			},
			handleMore() {
				this.checkLoginAndNavigate("/pages/goods/goods");
			},
		}
	}
</script>

<style lang="scss">

	.container {
		height: 100vh;
		padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
		box-sizing: border-box;
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #F8F9FA 26%);
	}

	.search-wrap {
		margin: 0 28rpx;
		border: 1rpx solid rgba(198, 109, 29, 1);
		background-color: #fff;
		border-radius: 82rpx;
		height: 82rpx;
		display: flex;
		align-items: center;
		padding: 0 34rpx;
		opacity: .8;

		input {
			flex: 1;
			font-size: 26rpx;
			color: #333333;
		}
	}

	.nav-wrap {
		display: grid;
		grid-gap: 20rpx;
		padding: 28rpx 28rpx;


		.nav-row {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 20rpx;

			image {
				width: 100%;
				border-radius: 24rpx;
				object-fit: cover;
				transition: transform .3s ease-in-out;

				&:active {
					transform: scale(.9);
				}
			}
		}
	}

	.more-button {
		display: flex;
		align-items: center;
		font-size: 22rpx;
		color: rgba(0, 0, 0, 0.4);
	}

	.list-wrap {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 20rpx;
		padding: 20rpx 28rpx;
	}

	.card-box {
		width: 100%;
		overflow: hidden;
		border-radius: 28rpx;
		background: rgba(255, 255, 255, 1);
		box-shadow: 0px -3rpx 6rpx rgba(173, 174, 179, 0.09), 0px 3rpx 6rpx rgba(173, 174, 179, 0.09);
		display: flex;
		flex-direction: column;

		image {
			flex-shrink: 0;
			border-top-left-radius: 28rpx;
			border-top-right-radius: 28rpx;
		}

		.card-content {
			display: flex;
			flex-direction: column;
			padding: 14rpx 18rpx;
			overflow: hidden;

			.card-content_title {
				font-size: 34rpx;
				color: #333333;
				padding-bottom: 10rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden
			}

			.card-content_row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.js {
					font-size: 34rpx;
					font-weight: 400;
					letter-spacing: 0px;
					color: #FF6600;
				}
			}
		}
	}
</style>
