<template>
	<image class="rui-avatar" :style="style" :src="url"  @click="handleClick" :key="url"></image>
</template>

<script>
	export default {
		emits:["click"],
		name:"rui-avatar",
		props:{
			size:{
				type:String,
				default:"120rpx"
			},
			src:{
				type:String,
			},
		},
		computed:{
			style(){
				return `width: ${this.size};height:${this.size};`
			},
			url(){
				return process.env.VUE_APP_BASE_API + this.src
			}
		},
		methods:{
			handleClick(e){
				this.$emit("click",e)
			}
		}
	}
</script>

<style lang="scss">
.rui-avatar{
	border-radius: 50%;
	flex-shrink: 0;
	object-fit: cover;
}
</style>