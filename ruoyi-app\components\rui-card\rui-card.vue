<template>
	<view class="rui-card" :style="style">
		<view class="rui-card_title" v-if="title">{{title}}</view>
		<view class="rui-card_content">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-card",
		props: {
			radius: {
				type: String,
				default: "20rpx"
			},
			bg: {
				type: String,
				default: "#fff"
			},
			title: {
				type: String,
				default: ""
			},
			padding: {
				type: String,
				default: "20rpx 28rpx"
			},
			marginTop: {
				type: String,
				default: "0"
			},
			marginBottom: {
				type: String,
				default: "24rpx"
			},
		},
		computed:{
			style(){
				return `border-radius:${this.radius};background:${this.bg};padding:${this.padding};margin-top:${this.marginTop};margin-bottom:${this.marginBottom};`
			}
		}
	}
</script>

<style lang="scss">
	.rui-card {
		border-radius: 20rpx;
		background: rgba(255, 255, 255, 1);
		.rui-card_title{
			padding-bottom:10rpx ;
			font-size: 26rpx;
			font-weight: bold;
			color: #555555;
		}
	}
</style>