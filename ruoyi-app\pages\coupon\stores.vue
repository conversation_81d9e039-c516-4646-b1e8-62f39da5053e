<template>
  <view class="container">
		<view class="stores">
			<view class="store-item" v-for="(item,index) in list" :key="index">
				<image class="store-logo" :src="format_url(item.storeLogo)"></image>
				<view class="content">
					<view class="store-title">{{item.storeName}}</view>
					<view class="store-location">{{item.location}}</view>
				</view>
			</view>
		</view>
  </view>
</template>

<script>
	import {getCouponStores} from '@/api/common.js'
export default {
  data() {
    return {
			packageId:null,
			list:[]
		}
  },
  onLoad({packageId}) {
		this.packageId = packageId
		this.getCouponStores()
  },
  methods: {
		getCouponStores(){
			getCouponStores(this.packageId).then(res=>{
				this.list = res.data
			})
		},
		format_url(url) {
			const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
			const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
			return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
		},
  }
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #F5F5F5;
	height: 100vh;
	.stores{
		height: 100%;
		overflow-y: scroll;
		padding: 24rpx 30rpx;
		.store-item{
			display: flex;
			align-items: center;
			padding: 20rpx 30rpx;
			background-color: #fff;
			border-radius: 18rpx;
			margin-bottom: 28rpx;
			.store-logo{
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				margin-right: 20rpx;
				object-fit: cover;
			}
			.content{
				flex: 1;
				.store-title{
					font-size: 30rpx;
					font-weight: bold;
					margin-bottom: 10rpx;
					color: #2E2E2E;
				}
				.store-location{
					font-size: 24rpx;
					color: #6C6C6C;
				}
			}
		}
	}
}
</style>
