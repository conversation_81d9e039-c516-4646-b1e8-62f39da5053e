<template>
	<view class="container">
		<map id="map" :latitude="currentLocation.latitude" :longitude="currentLocation.longitude" :scale="scale"
			:markers="markers" :show-location="true" :enable-zoom="true" :enable-scroll="true" :show-compass="true"
			style="width: 100%; height: 100%;">
		</map>
		<view class="fixed-popup">
			<view class="line"></view>
			<image class="car-image" src="../../static/images/car.png" mode="heightFix"></image>
			<view class="card-wrap">
				<view class="car-info">
					<view class="car-num">{{form.plateNo ? form.plateNo :'请设置车牌'}}</view>
					<view class="car-local">
						<image src="../../static/images/icon/map-icon.png" mode="heightFix"></image>
						<text>{{form.parkName}}</text>
					</view>
					<view class="car-time">
						<view class="label">停车时长</view>
						<view class="time">
						  <block v-for="(item, index) in timeDiff" :key="index">
						    <text class="num">{{ item.num }}</text>
						    <!-- 如果不是最后一个单位，显示分割符 -->
						    <text v-if="index !== timeDiff.length - 1" class="split">{{ item.unit }}</text>
						  </block>
						</view>

						<view class="price">
							<text>{{form.totalAmount?form.totalAmount / 100:'0.00'}}</text>
							<text>元</text>
						</view>
					</view>
				</view>
				<view class="car-action">
					<view class="button" @click="handleSetCarNum">设置车牌</view>
					<view class="button" style="background-color: rgba(254, 248, 79, 1);" @click="handlePay">停车缴费</view>
				</view>
			</view>
		</view>
		<keyboard-plate ref="plateNober" :plateNo.sync='form.plateNo' @change="getplateNo" safeArea isShow></keyboard-plate>
	</view>
</template>

<script>
	import { getParkInfo } from '@/api/common.js'
	export default {
		data() {
			return {
				timeDiff: {
					hours: '00',
					minutes: '00',
					seconds: '00'
				},
				timer: null,
				form: {
					plateNo: '',
					parkName: "",
					entryTime: "2024-09-20 10:20:30"
				},
				scale: 16,
				currentLocation: {
					latitude: 39.842928,
					longitude: 116.422883,
				},
				markers: [{
					id: 1,
					latitude: 39.842928,
					longitude: 116.422883,
					width: 30,
					heigth: 60,
				}],
				timeDiff:[]
			};
		},
		onLoad() {
			this.getUserLocation();
			this.form.plateNo = uni.getStorageSync('plateNo')
			this.getInfo()
		},
		onUnload() {
			this.timer && clearInterval(this.timer);
		},
		onHide() {
			this.timer && clearInterval(this.timer);
		},
		methods: {
			getInfo() {
				if (this.form.plateNo) {
					uni.showLoading()
					getParkInfo({ plateNo: this.form.plateNo }).then(res => {
						this.form = res.data
							this.timer = setInterval(this.updateTimeDiff, 1000);
					}).catch(()=>{
						clearInterval(this.timer);
						this.timer = null;
					}).finally(()=>{
						uni.hideLoading()
					})
				} else {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
			updateTimeDiff() {
				const now = new Date();
				const entryTime = new Date(this.form.entryTime);
				const diff = now - entryTime;

				const years = Math.floor(diff / (1000 * 60 * 60 * 24 * 365));
				const months = Math.floor((diff % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30));
				const days = Math.floor((diff % (1000 * 60 * 60 * 24 * 30)) / (1000 * 60 * 60 * 24));
				const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
				const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
				const seconds = Math.floor((diff % (1000 * 60)) / 1000);

				// 清空 timeDiff 数组
				this.timeDiff = [];

				// 根据实际时间单位动态生成
				if (years > 0) this.timeDiff.push({ num: years.toString().padStart(2, '0'), unit: '年' });
				if (months > 0) this.timeDiff.push({ num: months.toString().padStart(2, '0'), unit: '个月' });
				if (days > 0) this.timeDiff.push({ num: days.toString().padStart(2, '0'), unit: '天' });
				if (hours > 0) this.timeDiff.push({ num: hours.toString().padStart(2, '0'), unit: '小时' });
				if (minutes > 0) this.timeDiff.push({ num: minutes.toString().padStart(2, '0'), unit: '分钟' });
				if (seconds >= 0) this.timeDiff.push({ num: seconds.toString().padStart(2, '0'), unit: '秒' });
			},
			handlePay() {
				uni.showToast({
					title: '敬请期待！',
					icon: 'none'
				})
			},
			handleSetCarNum() {
				this.$refs.plateNober.open(this.form.plateNo);
			},
			getplateNo({ value }) {
				uni.setStorageSync('plateNo', value)
				this.form.plateNo = value
				this.getInfo()
			},
			// 获取用户位置
			getUserLocation() {
				uni.getLocation({
					type: 'gcj02', // 适用于微信小程序的坐标系
					success: (res) => {
						this.currentLocation.latitude = res.latitude;
						this.currentLocation.longitude = res.longitude;
					}
				});
			},
			watchUserLocation() {
				this.locationInterval = setInterval(() => {
					this.getUserLocation();
				}, 5000);
			}
		},
		onReady() {
			this.watchUserLocation();
		},
	};
</script>


<style lang="scss" scoped>
	.container {
		height: 100vh;
		position: relative;

		.fixed-popup {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			width: 100%;
			z-index: 9;
			border-top-left-radius: 50rpx;
			border-top-right-radius: 50rpx;
			padding: 60rpx 28rpx env(safe-area-inset-bottom);
			box-sizing: border-box;
			color: rgba(31, 33, 41, 1);
			background: linear-gradient(180deg, rgba(200, 229, 253, 1) 0%, rgba(243, 245, 248, 1) 14.65%);

			.line {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				top: 24rpx;
				width: 64rpx;
				height: 10rpx;
				opacity: 1;
				border-radius: 10rpx;
				background: rgba(7, 41, 70, 1);
			}

			.car-image {
				position: absolute;
				right: 50rpx;
				top: 20rpx;
				height: 180rpx;
			}

			.card-wrap {
				background-color: #fff;
				border-radius: 16rpx;
				padding: 32rpx;
				width: 100%;
				box-sizing: border-box;

				.car-info {
					.car-num {
						font-size: 48rpx;
						font-weight: 500;
						letter-spacing: 0px;
						line-height: 64rpx;
						color: rgba(7, 41, 70, 1);
					}

					.car-local {
						font-size: 26rpx;
						font-weight: 400;
						letter-spacing: 0px;
						padding: 16rpx 0 26rpx;
						display: flex;
						align-items: center;

						image {
							height: 30rpx;
							margin-right: 10rpx;
						}
					}

					.car-time {
						display: flex;
						align-items: center;

						.label {
							margin-right: 16rpx;
							flex-shrink: 0;
						}

						.time {
							flex: 1;
							color: rgba(255, 51, 51, 1);
							display: flex;
							flex-wrap: nowrap;
							overflow-x: scroll;

							.num {
								width: 44rpx;
								height: 44rpx;
								text-align: center;
								line-height: 44rpx;
								border-radius: 40%;
								background: rgba(255, 51, 51, 0.1);
								font-size: 26rpx;
								white-space: nowrap;
								flex-shrink: 0;
							}

							.split {
								flex-shrink: 0;
								margin: 0 8rpx;
							}
						}
					}

					.price {
						color: rgba(255, 51, 51, 1);

						text:nth-child(1) {
							font-size: 48rpx;
							font-weight: bold;
						}

						text:nth-child(2) {
							font-size: 24rpx;
							margin-left: 6rpx;
						}
					}
				}

				.car-action {
					display: grid;
					grid-template-columns: 1fr 1fr;
					grid-gap: 20rpx;
					margin-top: 48rpx;

					.button {
						border-radius: 16px;
						background: rgba(243, 245, 248, 1);
						height: 88rpx;
						line-height: 88rpx;
						text-align: center;
						font-size: 28rpx;
						font-weight: 500;
						letter-spacing: 0px;
						transition: all .3s;

						&:active {
							transform: scale(.98);
						}
					}
				}
			}
		}
	}
</style>