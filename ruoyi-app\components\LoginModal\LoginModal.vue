<template>
	<!-- 登录弹窗组件 -->
	<uni-popup ref="modal" type="bottom" :safeArea="false" backgroundColor="#fff" borderRadius="26rpx 26rpx 0 0" @change="toggleModal">
		<view class="login-modal">
			<image class="modal-logo" src="../../static/logo.png"></image>
			<view class="welcome-text">欢迎加入</view>
			<view class="join-text">加入后享专属活动&积分兑礼</view>
			<view class="modal-buttons">
				<rui-button :open-type="check?'getPhoneNumber':''" @click="handleLogin">手机号一键登录</rui-button>
				<rui-button :plain="true" @click="close">暂时跳过</rui-button>
			</view>

			<view :animation="animationData" class="shake-element">
				<view class="modal-foot" @click="check=!check">
					<view class="radio" :class="{'radio-check':check}">
						<uni-icons v-if="check" type="checkmarkempty" color="#fff" size="12"></uni-icons>
					</view>
					<p> 允许我们在必要场景下，合理使用您的个人信息，且阅读并同意<text @tap.stop="handleToPrivacy">《隐私条款》</text><text
							@tap.stop="handleToUserAgrement">《会员协议》</text>等内容</p>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		name: "LoginModal",
		computed: {
			...mapState('app', {
				loginModal: state => state.loginModal, 
			}),
		},
		watch: {
			loginModal: {
				handler(val,old) {
					if(val===old)return
					this.$nextTick(() => {
						if (val) {
							this.open()
						} else {
							this.close()
						}
					})
				},
				immediate: true
			}
		},
		data() {
			return {
				check: false,
				animationData: {}
			};
		},
		methods: {
			...mapActions('user',['WxLogin', "GetInfo"]),
			toggleModal(e) {
				this.$store.commit('app/toggleLoginModal', e.show)
			},
			// 微信授权登录
			async handleLogin(e) {
				console.log(e);
				if (!this.check) return this.shakeElement()
				if (e.detail.errMsg === "getPhoneNumber:fail no permission") {
					uni.showToast({ title: "无权限，请联系商户！", icon: "none" })
				}
				if (e.detail.errMsg !== "getPhoneNumber:ok") return;
				const [, res] = await uni.login();
				uni.showLoading({ title: "登陆中..." })
				const { code, msg } = await this.WxLogin({ code: e.detail.code, js_code: res.code })
				if (code === 200) {
					await this.GetInfo()
					this.$refs.modal.close();
					uni.hideLoading()
				} else {
					uni.showToast({ title: msg, icon: "none" })
				} 
			},
			// 隐私协议
			handleToPrivacy() {
				uni.navigateTo({
					url:`/pages/contract/contract?title=隐私协议&type=privacy_contract`
				})
			},
			handleToUserAgrement() {
				uni.navigateTo({
					url:`/pages/contract/contract?title=用户协议&type=user_contract`
				})
			},
			open() {
				this.$refs.modal.open();
			},
			close() {
				this.$refs.modal.close();
			},
			// 创建左右晃动的动画
			shakeElement() {
				uni.vibrateShort();
				let animation = uni.createAnimation({
					duration: 100, // 每次晃动的动画时长
					timingFunction: 'linear' // 线性变化
				});

				let shakeTimes = 6; // 总晃动次数
				let currentTime = 0; // 当前次数

				const interval = setInterval(() => {
					currentTime++;
					let offset = currentTime % 2 === 0 ? -5 : 5;
					animation.translateX(offset).step();
					this.animationData = animation.export();
					if (currentTime >= shakeTimes) {
						clearInterval(interval);
						animation.translateX(0).step();
						this.animationData = animation.export();
					}
				}, 100);
			}
		}
	}
</script>

<style lang="scss">
	.login-modal {
		display: flex;
		flex-direction: column;
		padding: 40rpx 40rpx env(safe-area-inset-bottom);
		color: #333333;
		

		.modal-logo {
			width: 92rpx;
			height: 92rpx;
			object-fit: cover;
			border-radius: 50%;
		}

		.welcome-text {
			font-size: 36rpx;
			font-weight: 600;
			padding: 8rpx 0 10rpx;
		}

		.join-text {
			font-size: 24rpx;
			color: #666666;
		}

		.modal-buttons {
			padding: 40rpx 50rpx;
			display: grid;
			grid-template-columns: repeat(1, 1fr);
			grid-row-gap: 20rpx;
		}

		.modal-foot {
			font-size: 24rpx;
			line-height: 40rpx;
			display: flex;

			text {
				color: $uni-color-primary;
			}

			.radio {
				margin-top: 4rpx;
				flex-shrink: 0;
				width: 34rpx;
				height: 34rpx;
				overflow: hidden;
				text-align: center;
				border-radius: 50%;
				border: 2rpx solid #808080;
				margin-right: 12rpx;
				display: inline-block;
				box-sizing: border-box;
				line-height: 34rpx;
			}

			.radio-check {
				border: 2rpx solid $uni-color-primary;
				background-color: $uni-color-primary;
			}
		}

	}
</style>