<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList">
			<template #top>
				<rui-nav-bar title="核销记录" back></rui-nav-bar>
				<rui-vtab v-model="currentTab" bg="#fff" :tabs="tabs" overspread @change="changeTab"></rui-vtab>
			</template>
			<view class="list-wrap">
				<view class="list-item" v-for="(item,index) in list" :key="index">
					<view class="list-item_head">
						<text>核销时间</text>
						<text class="time" style="color: #000;">{{item.updateTime}}</text>
					</view>
					<view class="list-preview" v-if="currentTab==='1'">
						<view class="preview-item">
							<text>商品编码</text>
							<text>{{item.goods.goodsId}}</text>
						</view>
						<view class="preview-item">
							<text>商品名称</text>
							<text>{{item.goods.goodsName}}</text>
						</view>
						<view class="preview-item">
							<text>积分数量</text>
							<text>{{item.price}}积分</text>
						</view>
						<view class="preview-item">
							<text>用户账号</text>
							<text>{{item.createBy}}</text>
						</view>
						<view class="preview-item">
							<text>核销账号</text>
							<text>{{item.updateBy}}</text>
						</view>
					</view>
					<view class="list-preview" v-if="currentTab==='2'">
						<view class="preview-item">
							<text>优惠卷编码</text>
							<text>{{item.couponId}}</text>
						</view>
						<view class="preview-item">
							<text>卡包名称</text>
							<text>{{item.couponPackage.packageName}}</text>
						</view>
						<view class="preview-item">
							<text>核销金额</text>
							<text>{{item.couponPackage.discountAmount}}元</text>
						</view>
						<view class="preview-item">
							<text>用户账号</text>
							<text>{{item.createBy}}</text>
						</view>
						<view class="preview-item">
							<text>核销账号</text>
							<text>{{item.updateBy}}</text>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
	import { getOrderRecord, getCouponRecord } from "@/api/common.js";
	export default {
		data() {
			return {
				tabs: [
					{ label: "商品核销", value: "1" },
					{ label: "优惠卷核销", value: "2" },
				],
				currentTab: "1",
				list: [],
			}
		},
		methods: {
			changeTab(e) {
				setTimeout(() => {
					this.$refs.paging.reload();
				}, 50)
			},
			handlePreview(images) {
				if (!images) {
					uni.showToast({
						title: '没有图片',
						icon: 'none'
					})
					return
				}
				const urls = images.split(',').map(url => {
					return this.format_url(url)
				})
				uni.previewImage({
					urls: urls
				})
			},
			format_url(url) {
				const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
				const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
				return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
			},
			getList(pageNo, pageSize) {
				if (this.currentTab === '1') {
					getOrderRecord({ pageNum: pageNo, pageSize }).then(res => {
						this.$refs.paging.complete(res.rows);
					})
				} else {
					getCouponRecord({ pageNum: pageNo, pageSize }).then(res => {
						this.$refs.paging.complete(res.rows);
					})
				}
			},
		}
	}
</script>

<style lang="scss">
	page {
		// background: #fff;
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #f7f8fa 36%);
	}

	.list-item {
		background-color: #fff;
		margin: 28rpx 28rpx;
		padding: 24rpx 28rpx 10rpx;
		color: #333333;
		border-radius: 28rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0px -3rpx 6rpx rgba(173, 174, 179, 0.09), 0px 3rpx 6px rgba(173, 174, 179, 0.09);
		// background: linear-gradient(170deg, rgba(252, 234, 212, 0.6) 0%, #fff 34%);


		.list-item_head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 28rpx;
			border-bottom: 1px solid rgba(236, 236, 236, 1);
			font-size: 24rpx;

			text:nth-child(1) {
				font-weight: 400;
				color: rgba(153, 153, 153, 1);
			}
		}

		.line {
			width: 100%;
			border-top: 2rpx dashed rgba(236, 236, 236, 1);
		}

		.list-preview {
			padding-top: 10rpx;

			.preview-item {
				padding: 16rpx 0;
				display: flex;
				justify-content: space-between;

				text:nth-child(1) {
					font-size: 24rpx;
					color: #323233;
					flex-shrink: 0;
				}

				text:nth-child(2) {
					padding-left: 70rpx;
					font-size: 24rpx;
					color: #646566;
					text-align: right;
				}
			}
		}
	}
</style>