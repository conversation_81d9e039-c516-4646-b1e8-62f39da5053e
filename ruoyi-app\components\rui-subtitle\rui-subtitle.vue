<template>
	<view class="rui-subtitle" :style="{padding,margin}">
		<text class="rui-subtitle_text">{{title}}</text>
		<slot></slot>
	</view>
</template>

<script>
	export default {
		name: "rui-subtitle",
		props: {
			title: {
				type: String,
				default: ''
			},
			padding: {
				type: String,
				default: '0 0 0 28rpx'
			},
			margin:{
				type: String,
				default: '0 28rpx 20rpx'
			}
		},
	}
</script>

<style lang="scss">
	.rui-subtitle {
		color: #0C1019;
		font-size: 32rpx;
		font-weight: 500;
		position: relative;
		display: flex;
		align-items: center;
		.rui-subtitle_text{
			flex: 1;
		}
		&:before {
			content: "";
			width: 8rpx;
			height: 34rpx;
			background-color: #E0AC7D;
			border-radius: 8rpx;
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
		}
	}
</style>