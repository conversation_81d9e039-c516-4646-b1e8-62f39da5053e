<template>
	<view class="help-container">
		<view v-for="(item, findex) in list" :key="findex" :title="item.title" class="list-title">
			<view class="text-title">
				<view :class="item.icon"></view>{{ item.title }}
			</view>
			<view class="childList">
				<view v-for="(child, zindex) in item.childList" :key="zindex" class="question" hover-class="hover"
					@click="handleText(child)">
					<view class="text-item">{{ child.title }}</view>
					<view class="line" v-if="zindex !== item.childList.length - 1"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					icon: 'iconfont icon-help',
					title: '支付问题',
					childList: [{
						title: '支持哪些支付方式？',
						content: '我们支持支付宝、微信支付和银行卡支付。'
					}, {
						title: '支付时遇到问题怎么办？',
						content: '请检查网络连接或尝试使用其他支付方式。如果问题仍然存在，请联系客服。'
					}, {
						title: '可以开发票吗？',
						content: '可以。请在支付页面选择“需要发票”选项，并填写相关信息。'
					}]
				}, {
					icon: 'iconfont icon-help',
					title: '账户问题',
					childList: [{
						title: '如何注册账户？',
						content: '点击首页的“注册”按钮，填写必要的信息即可完成注册。'
					}, {
						title: '忘记密码怎么办？',
						content: '请点击登录页面的“忘记密码”链接，按照提示找回密码。'
					}, {
						title: '如何修改个人信息？',
						content: '登录后进入“我的账户”，选择“编辑资料”进行修改。'
					}]
				}, {
					icon: 'iconfont icon-help',
					title: '内容问题',
					childList: [{
						title: '知识内容是否可以下载？',
						content: '部分知识内容支持下载，具体请查看知识内容详情页的说明。'
					}, {
						title: '购买的知识内容有有效期吗？',
						content: '大多数知识内容永久有效，具体请参见知识内容详情页的说明。'
					}, {
						title: '知识内容有退款政策吗？',
						content: '知识内容购买后一般不支持退款，特殊情况请联系客服。'
					}]
				}, {
					icon: 'iconfont icon-help',
					title: '客户支持',
					childList: [{
						title: '如何联系客服？',
						content: '您可以通过网站底部的“联系我们”页面找到客服联系方式。'
					}, {
						title: '客服的工作时间是怎样的？',
						content: '客服工作时间为周一至周五，9:00 至 18:00。'
					}, {
						title: '是否有在线客服？',
						content: '有。您可以在网站右下角找到在线客服入口。'
					}]
				},{
					icon: 'iconfont icon-help',
					title: '反馈问题',
					childList: [{
						title: '如何提交反馈？',
						content: '您可以在网站底部的“反馈”页面填写并提交您的建议和意见。'
					}, {
						title: '反馈处理时间是多久？',
						content: '我们会在3个工作日内处理并回复您的反馈。'
					}, {
						title: '反馈的内容会公开吗？',
						content: '一般情况下，您的反馈内容不会公开，除非您同意公开。'
					}]
				}, {
					icon: 'iconfont icon-help',
					title: '隐私问题',
					childList: [{
						title: '如何保护我的隐私？',
						content: '我们严格遵守隐私政策，确保您的个人信息安全。'
					}, {
						title: '我的信息会不会被第三方使用？',
						content: '我们不会将您的个人信息透露给第三方，除非法律要求。'
					}, {
						title: '如何查看隐私政策？',
						content: '您可以在网站底部找到并查看我们的隐私政策。'
					}]
				}]

			}
		},
		methods: {
			handleText(item) {
				this.$tab.navigateTo(`/pages/common/textview/index?title=${item.title}&content=${item.content}`)
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #f8f8f8;
	}

	.help-container {
		margin-bottom: 100rpx;
		padding: 30rpx;
	}

	.list-title {
		margin-bottom: 30rpx;
	}

	.childList {
		background: #ffffff;
		box-shadow: 0px 0px 10rpx rgba(193, 193, 193, 0.2);
		border-radius: 16rpx;
		margin-top: 10rpx;
	}

	.line {
		width: 100%;
		height: 1rpx;
		background-color: #F5F5F5;
	}

	.text-title {
		color: #303133;
		font-size: 32rpx;
		font-weight: bold;
		margin-left: 10rpx;

		.iconfont {
			font-size: 16px;
			margin-right: 10rpx;
		}
	}

	.text-item {
		font-size: 28rpx;
		padding: 24rpx;
	}

	.question {
		color: #606266;
		font-size: 28rpx;
	}
</style>