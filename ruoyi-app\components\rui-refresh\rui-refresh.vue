<template>
	<z-paging ref="paging" show-refresher-when-reload :auto="true" use-inner-list :paging-style="pagingStyle"
		v-model="list" @query="queryList" use-page-scroll :fixed="false">

		<template #refresher="{refresherStatus}">
			<view class="refresher" style="position: relative;">
				<zero-loading type="atom" position="absolute"></zero-loading>
			</view>
		</template>

		<template #loadingMoreNoMore>
			<view class="noMore">
				<view class="noMore-line"></view>
				<text>没有更多数啦</text>
				<view class="noMore-line"></view>
			</view>
		</template>

		<slot :list="list"></slot>
		<!-- 		<view class="item" v-for="(item,index) in list" :key="index">
			<slot name="item" :item="item" :index="index"></slot>
		</view> -->
	</z-paging>
</template>

<script>
	export default {
		name: "rui-refresh",
		props: {
			require: {
				type: [Promise, Function],
			},
			pagingStyle: {
				type: Object
			},
			query: {
				type: Object,
			},
		},
		data() {
			return {
				list: []
			};
		},
		methods: {
			reload() {
				this.$refs.paging.reload();
			},
			queryList(pageNo, pageSize) {
				this.require({ pageNo, pageSize, ...this.query }).then(res => {
					if (res.code === 200) {
						this.$refs.paging.completeByTotal(res.rows, res.total);
					} else {
						this.$refs.paging.complete(false);
					}
				}).catch(() => {
					// 如果请求失败写this.$refs.paging.complete(false)，会自动展示错误页面
					this.$refs.paging.complete(false);
				})

			}

		}
	}
</script>

<style lang="scss">
	.noMore {
		font-size: 24rpx;
		color: #555555;
		padding-top: 50rpx;
		padding-bottom: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		.noMore-line {
			width: 120rpx;
			height: 0.5rpx;
			background-color: rgba(0, 0, 0, 0.02);
		}

		text {
			margin: 0 50rpx;
			flex-shrink: 0;
		}
	}

	.refresher {
		padding: 80rpx 0;
	}
</style>