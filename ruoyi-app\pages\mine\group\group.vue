<template>
	<view class="container">
		<z-paging ref="paging" v-model="list" @query="getList">
			<template #top>
				<rui-nav-bar title="社群列表" back></rui-nav-bar>
				<rui-tab v-model="currentTab" :tabs="tabs" bg="#fff" @change="()=>$refs.paging.reload()"></rui-tab>
			</template>
			<view class="group">
				<view class="group-item" v-for="(item,index) in list" :key="index">
					<rui-image-preview class="group-cover" :src="item.groupCover"></rui-image-preview>
					<view class="group-info">
						<view class="group-title">
							<uni-icons color="#40AE36" size="26" type="weixin"></uni-icons>
							<text>{{item.groupName}}</text>
							<rui-dict-tag :options="options" :value="item.type"></rui-dict-tag>
						</view>
						<view class="group-row">
							<uni-icons type="staff" color="rgba(153, 153, 153, 1)" size="20"></uni-icons>
							<view class="group-user">{{item.total}}</view>
						</view>
					</view>
					<rui-button width="130rpx" type="primary" height="50rpx" size="22" @click="handleClick(item)">加群</rui-button>
				</view>
			</view>
		</z-paging>

		<uni-popup ref="popup" type="center" border-radius="10px 10px 0 0">
			<view class="card-wrap">
				<image style="width: 100%;" :src="qrcodeUrl" mode="widthFix" show-menu-by-longpress></image>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { listChatGroup } from '@/api/common.js'
	import { dict } from '@/api/login.js'
	export default {
		computed: {
			qrcodeUrl() {
				return process.env.VUE_APP_BASE_API + this.form.qrcodeUrl
			}
		},
		data() {
			return {
				list: [],
				options: [],
				form: {}
			}
		},
		onLoad() {
			this.getDict()
		},
		methods: {
			getDict() {
				dict("group_type").then(res => {
					this.options = res.data
				})
			},
			handleClick(item) {
				this.form = item
				this.$refs.popup.open()
			},
			getList(pageNo, pageSize) {
				listChatGroup({ pageNum: pageNo, pageSize }).then(res => {
					this.$refs.paging.complete(res.rows.map(item => {
						item.speen = Math.ceil((item.total - item.residueTotal) / item.total * 100)
						return item;
					}));
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		// background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #F8F9FA 26%);
		box-sizing: border-box;
		background-color: #f7f8fa;
		height: 100vh;
		display: flex;
		flex-direction: column;

		.group {
			padding: 20rpx 28rpx;

			.group-item {
				display: flex;
				align-items: center;
				background: linear-gradient(170deg, rgba(252, 234, 212, 0.5) 0%, #fff 64%);
				border-radius: 28rpx;
				padding: 24rpx 28rpx;

				.group-cover {
					width: 140rpx;
					height: 140rpx;
					object-fit: cover;
				}

				.group-info {
					flex: 1;
					padding: 20rpx 20rpx;
					display: flex;
					alsign-items: center;
					flex-direction: column;

					.group-title {
						display: flex;
						align-items: center;

						text:nth-child(2) {
							margin-left: 10rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							font-size: 32rpx;
							color: #323232;
							font-weight: bold;
							margin-right: 14rpx;
						}
					}

					.group-row {
						padding-top: 10rpx;
						display: flex;
						align-items: center;
						color: rgba(153, 153, 153, 1);

						.group-user {
							padding: 0 16rpx;
							font-size: 34rpx;
						}
					}
				}
			}
		}
	}

	.card-wrap {
		width: 600rpx;
		height: 600rpx;
		background-color: #fff;
		background: linear-gradient(170deg, rgba(252, 234, 212, 0.8) 0%, #fff 64%);
		border-radius: 40rpx;
	}
</style>