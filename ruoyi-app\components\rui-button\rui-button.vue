<template>
	<button :class="buttonClass" :type="type" :style="style" :open-type="openType" @getphonenumber="getphonenumber" @tap.stop="handleClick" :disabled="disabled">
		<slot></slot>
	</button>
</template>

<script>
	export default {
		name: "rui-button",
		emits: ['click', 'getphonenumber'],
		props: {
			type: {
				type: String,
				default: "primary"
			},
			openType:{
				type: String,
				default: ""
			},
			width: {
				type: String,
				default: "auto"
			},
			height: {
				type: String,
				default: "88rpx"
			},
			size: {
				type: [Number, String],
				default: "28"
			},
			radius: {
				type: [Number, String],
				default: "44"
			},
			disabled: {
				type: Boolean,
				default: false
			},
			plain: {
				type: Boolean,
				default: false
			},
			loading: {
				type: Boolean,
				default: false
			},
			
		},
		computed: {
			buttonClass() {
				return `rui-button rui-button-${this.type} ${this.plain&&'rui-button-plain'}`
			},
			style() {
				return `width:${this.width};height:${this.height};font-size:${this.size}rpx;border-radius: ${this.radius}rpx;`
			}
		},
		methods: {
			handleClick(event) {
				this.$emit('click', event);
			},
			getphonenumber(event){
				this.$emit('click', event);
			}
		}
	}
</script>

<style lang="scss">
	.rui-button {
		cursor: pointer;
		outline: inherit;
		flex: 1;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #666666;
		box-sizing: border-box;
		font-size: 28rpx;
		font-weight: 500;
		cursor: pointer;
		letter-spacing: 4rpx;
		white-space: nowrap;

		&:active {
			opacity: .95;
		}

		&::after {
			display: none;
		}
	}

	.rui-button[type='primary']:not(.rui-button-plain) {
		background: $uni-color-primary;
		border: 2rpx solid $uni-color-primary;
		color: #fff;
	}

	.rui-button[type='primary'].rui-button-plain{
		border: 1rpx solid $uni-color-primary !important;
		color: $uni-color-primary !important;
		background-color: #fff !important;
	}
	
	.rui-button[type='warning']:not(.rui-button-plain) {
		background: $uni-color-warning;
		border: 2rpx solid $uni-color-warning;
		color: #fff;
	}
	
	.rui-button[type='warning'].rui-button-plain{
		border: 1rpx solid $uni-color-warning !important;
		color: $uni-color-warning !important;
		background-color: #fff !important;
	}
	
	.rui-button[type='success']:not(.rui-button-plain) {
		background: $uni-color-success;
		border: 2rpx solid $uni-color-success;
		color: #fff;
	}
	
	.rui-button[type='success'].rui-button-plain{
		border: 1rpx solid $uni-color-success !important;
		color: $uni-color-success !important;
		background-color: #fff !important;
	}
	
	.rui-button[type='danger']:not(.rui-button-plain) {
		background: $uni-color-danger;
		border: 2rpx solid $uni-color-danger;
		color: #fff;
	}
	
	.rui-button[type='danger'].rui-button-plain{
		border: 1rpx solid $uni-color-danger !important;
		color: $uni-color-danger !important;
		background-color: #fff !important;
	}
	

</style>