<template>
	<image @click="handlePreview" :src="realSrc" :style="{width,height,'object-fit':fit,'border-radius':radius}"></image>
</template>

<script>
	import { isExternal } from "@/utils/validate";
	export default {
		name: "rui-image-preview",
		props: {
			src: {
				type: String,
				default: ""
			},
			width: {
				type: [Number, String],
				default: "100%"
			},
			height: {
				type: [Number, String],
				default: "100%"
			},
			fit:{
				type: String,
				default: "cover"
			},
			preview: {
				type: Boolean,
				default: true
			},
			radius:{
				type: String,
				default: '0rpx'
			}
		},
		computed: {
			realSrc() {
				if (!this.src) {
					return;
				}
				let real_src = this.src.split(",")[0];
				return this.format_url(real_src)
			},
		},
		methods:{
			handlePreview(){
				if(!this.preview)return
				uni.previewImage({
					urls: [this.realSrc]
				})
			},
			format_url(url) {
				const hasDomain = /^(http:\/\/|https:\/\/|\/\/)/.test(url);
				const isBase64 = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(url);
				return hasDomain || isBase64 ? url : process.env.VUE_APP_BASE_API + url;
			}
		}
	}
</script>

<style lang="scss">

</style>