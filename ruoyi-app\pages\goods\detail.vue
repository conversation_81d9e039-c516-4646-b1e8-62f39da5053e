<template>
	<view class="container">
		<swiper :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000" style="hieght:500rpx">
			<swiper-item v-for="(item,index) in covers" :key="index">
				<image class="image-cover" :src="item"></image>
			</swiper-item>
		</swiper>
		<view class="card-wrap">
			<view class="goods-row">
				<view class="goods-name">{{form.goodsName}}</view>
				<view class="goods-price">{{form.goodsIntegral}} <text>积分</text> </view>
			</view>
		</view>
		<view class="card-wrap goods-info">
			<view class="card-row" v-if="form.goodsType === '1'">
				<text>代金券面值</text>
				<text>{{form.voucherFaceValue}} 元</text>
			</view>
			<view class="card-row">
				<text>每人限购</text>
				<text>{{form.goodsLimitCount}} 件</text>
			</view>
			<view class="card-row">
				<text>剩余库存</text>
				<text>{{form.goodsBalance}} 件</text>
			</view>
			<view class="card-row">
				<text>有效时间</text>
				<text>从兑换日期起算 {{form.effectiveDays}} 天</text>
			</view>
		</view>
		<rui-divider text="商品介绍"></rui-divider>
		<view class="card-info">{{form.goodsDetails || "无"}}</view>
		<rui-divider text="购买须知"></rui-divider>
		<view class="card-info">{{form.goodsExplain|| "无"}}</view>
		<rui-divider text="退货说明"></rui-divider>
		<view class="card-info">{{form.returnPolicy|| "无"}}</view>
		<!-- <rui-divider text="兑换地址"></rui-divider> -->
		<!-- <map :latitude="latitude" :longitude="longitude" :scale="16" :markers="markers" @markertap="navigateToLocation" 
      @callouttap="navigateToLocation"
			style="width: 100%; height: 400rpx;"></map> -->
		<rui-fixed-bottom>
			<rui-button type="default" @click="handleBack">返 回</rui-button>
			<rui-button type="primary" @click="handlePay" :disabled="form.goodsBalance<=0">立即兑换</rui-button>
		</rui-fixed-bottom>
	</view>
</template>

<script>
	import { goodsDetail, addOrder } from '@/api/common.js'
	export default {
		computed: {
			covers() {
				if (this.form.goodsImage) {
					return this.form.goodsImage.split(',').map(item => {
						return process.env.VUE_APP_BASE_API + item
					})
				}
				return []
			},
		},
		data() {
			return {
				form: {},
				latitude: 39.842928,
				longitude: 116.422883,
				markers: [{
					id: 1, // marker的id
					latitude: 39.842928, // 标记的纬度
					longitude: 116.422883, // 标记的经度

					callout: { // 可选：气泡提示框
						content: '点击导航',
						color: '#ffffff',
						fontSize: 12,
						borderRadius: 5,
						bgColor: '#007AFF',
						padding: 10,
						display: 'ALWAYS' // 设置气泡显示方式：ALWAYS 一直显示，BYCLICK 点击时显示
					}
				}]
			}
		},
		onLoad(options) {
			this.form.goodsId = options.goodsId
			this.getDetail()
		},

		methods: {
			// 用户点击标记时，进行导航
			navigateToLocation() {
				uni.openLocation({
					latitude: this.latitude,
					longitude: this.longitude,
					name: '汇琴购物中心',
					address: '北京市丰台区石榴庄街道榴乡路86号院汇琴购物中心',
					scale: 18
				});
			},
			handlePay() {
				uni.showModal({
					title: '提示',
					content: `确认消耗${this.form.goodsIntegral}积分兑换吗？`,
					success: (res) => {
						if (res.confirm) {
							const params = {
								goodsId: this.form.goodsId,
								goodsCount: 1
							}
							addOrder(params).then(res => {
								if (res.code === 200) {
									uni.showToast({
										title: '兑换成功',
										icon: 'success',
									})
									uni.navigateTo({
										url: "/pages/mine/order/detail?orderId=" + res.data.orderId,
									})
								} else {
									uni.showToast({
										title: res.msg,
										icon: 'error',
									})
								}
							})
						}
					}
				})
			},
			getDetail() {
				goodsDetail(this.form.goodsId).then(res => {
					if (res.data) {
						this.form = res.data
					} else {
						uni.navigateBack()
					}
				})
			},
			handleBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss">
	swiper {
		height: 500rpx;
	}

	.container {
		height: 100vh;
		overflow: hidden;
		background-color: #fafafa;
		overflow-y: scroll;
		padding-bottom: 15vh;

		.image-cover {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}

		.card-wrap {
			padding: 20rpx 28rpx;
			margin-bottom: 20rpx;
			background-color: #fff;

			.goods-row {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.goods-price {
					font-size: 44rpx;
					color: #ff0000;

					text {
						margin-left: 10rpx;
						font-size: 20rpx;
					}
				}

				.goods-name {
					font-size: 32rpx;
					font-weight: bold;
				}
			}

			.card-row {
				display: flex;
				align-items: center;
				padding: 25rpx 0;
				border-bottom: 1rpx solid #dcdee0;

				text:nth-child(1) {
					font-size: 28rpx;
					color: #c8c9cc;
					width: 110rpx;
					flex-shrink: 0;
					font-size: 26rpx;
				}

				text:nth-child(2) {
					padding-left: 20rpx;
					flex: 1;
					font-size: 28rpx;
					color: #646566;
				}
			}

			.card-row:last-child {
				border-bottom: none;
			}

		}

		.goods-info {
			padding: 0 40rpx;
		}

		.rui-fixed-bottom {
			display: grid;
			padding-left: 20rpx;
			padding-right: 20rpx;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 20rpx;
		}
	}

	.card-info {
		background-color: #fff;
		padding: 20rpx 28rpx;
		text-indent: 2em;
		font-size: 28rpx;
		line-height: 40rpx;
		color: #323232;
		overflow: hidden;
	}

	map {
		width: 100%;
		height: 330rpx;
		box-sizing: border-box;
		border-radius: 20rpx;
		margin-bottom: 180rpx;
	}
</style>