<template>
	<view class="container">
		<rui-sticky ref="sticky" background="transparent">
			<rui-nav-bar title="投诉建议" align="center" back></rui-nav-bar>
		</rui-sticky>
		<view class="head">
			<view class="head-title">倾听您的意见！</view>
			<view class="head-subtitle">此投诉为本小程序自有投诉渠道<br>非微信官方投诉渠道</view>
			<view class="image-pos">
				<uni-badge absolute="rightTop" type="primary" text="投诉记录">
					<image @click="handleDetail" src="@/static/images/icon/boot.png" mode="widthFix"></image>
				</uni-badge>
			</view>
		</view>
		<view class="form-wrap">
			<form @submit="formSubmit">
				<view class="form-item">
					<view class="form-item_label is-required">姓名</view>
					<view class="form-item_content">
						<input v-model="form.name" placeholder="请输入您的姓名" placeholder-class="placeholder" />
					</view>
				</view>
				<view class="form-item">
					<view class="form-item_label is-required">联系电话</view>
					<view class="form-item_content">
						<input v-model="form.phone" placeholder="请输入您的联系电话" placeholder-class="placeholder" />
					</view>
				</view>
				<view class="form-item">
					<view class="form-item_label is-required">投诉类型</view>
					<view class="form-item_content">
						<rui-picker v-model="form.type" dictType="complaint_type" placeholder="请选择您的投诉类型"
							title="请选择投诉类型"></rui-picker>
						<rui-icon name="menu" size="26"></rui-icon>
					</view>
				</view>
				<view class="form-item">
					<view class="form-item_label is-required">投诉内容</view>
					<view class="form-item_content">
						<textarea v-model="form.content" placeholder="请输入您的投诉内容" placeholder-class="placeholder" />
					</view>
				</view>
				<view class="form-item">
					<view class="form-item_label">投诉附件</view>
					<view class="form-item_content">
						<rui-upload v-model="form.images" :limit="3" :baseUrl="baseUrl" :headers="headers"
							action="/common/upload"></rui-upload>
					</view>
				</view>
			</form>
			<rui-fixed-bottom>
				<rui-button type="default" @click="handleBack">返 回</rui-button>
				<rui-button type="primary" @click="handleApply">提交申请</rui-button>
			</rui-fixed-bottom>
		</view>
	</view>
</template>

<script>
	import { addComplain } from '@/api/common.js'
	import { getToken } from '@/utils/auth.js'
	export default {
		computed: {
			baseUrl() {
				return process.env.VUE_APP_BASE_API
			},
			headers() {
				return {
					Authorization: 'Bearer ' + getToken()
				}
			}
		},
		data() {
			return {
				form: {
					name: "",
					phone: "",
					type: "",
					content: "",
					images: []
				},
				status: "3",
			};
		},
		onLoad() {
			setTimeout(() => {
				this.form.name = this.$store.state.user.user.nickName
				this.form.phone = this.$store.state.user.user.phonenumber
			}, 500)
		},
		methods: {
			handleDetail() {
				uni.navigateTo({
					url: '/pages/mine/feedback/detail'
				})
			},
			handleApply() {
				console.log(this.form.images);
				if (this.validateForm()) {
					const images = this.form.images ? this.form.images.join(",") : null
					const params = { ...this.form, images }
					addComplain(params).then(response => {
						uni.showToast({
							icon: "none",
							title: "提交成功，预计1~3个工作日答复！"
						});
						this.form = {
							name: "",
							phone: "",
							type: "",
							content: "",
							images: []
						}
					});
				}
			},
			handleBack() {
				uni.navigateBack()
			},
			validateForm() {
				// 验证姓名
				if (!this.form.name.trim()) {
					uni.showToast({
						icon: "none",
						title: "姓名不能为空"
					});
					return false;
				}
				if (!/^[\u4e00-\u9fa5a-zA-Z]+$/.test(this.form.name)) {
					uni.showToast({
						icon: "none",
						title: "姓名只能包含汉字和字母"
					});
					return false;
				}

				// 验证联系电话
				if (!this.form.phone.trim()) {
					uni.showToast({
						icon: "none",
						title: "联系电话不能为空"
					});
					return false;
				}
				if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
					uni.showToast({
						icon: "none",
						title: "联系电话格式不正确"
					});
					return false;
				}

				// 验证投诉类型
				if (!this.form.type.trim()) {
					uni.showToast({
						icon: "none",
						title: "投诉类型不能为空"
					});
					return false;
				}

				// 验证投诉内容
				if (!this.form.content.trim()) {
					uni.showToast({
						icon: "none",
						title: "投诉内容不能为空"
					});
					return false;
				}

				return true; // 所有验证通过
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #F8F9FA 36%);
	}

	.uni-badge--primary {
		background-color: rgba(224, 172, 125, 1) !important;
	}

	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;

		.head {
			padding: 80rpx 32rpx;
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.head-title {
				font-size: 42rpx;
				font-weight: 600;
				color: rgba(52, 57, 101, 1);
			}

			.head-subtitle {
				padding-top: 20rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: rgba(76, 83, 148, 1);
			}

			.image-pos {
				position: absolute;
				right: 32rpx;
				top: 50%;
				transform: translateY(-50%);

				image {
					width: 180rpx;
				}
			}
		}

		.form-wrap {
			margin-top: 20rpx;
			flex: 1;
			border-top-left-radius: 56rpx;
			border-top-right-radius: 56rpx;
			background-color: #fff;
			padding: 60rpx 28rpx 200rpx;

			.form-item {
				display: flex;
				flex-direction: column;
				margin-bottom: 28rpx;

				.form-item_label {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					color: rgba(51, 51, 51, 1);
					padding-bottom: 20rpx;
				}

				.is-required::before {
					content: "*";
					color: #FF0000;
					margin-right: 10rpx;
					font-size: 30rpx;
					font-weight: bold;
				}

				.form-item_content {
					padding: 24rpx 30rpx;
					border-radius: 15rpx;
					background: rgba(248, 248, 248, 1);
					display: flex;

					input {
						font-size: 28rpx;
						flex: 1;
					}

					rui-picker {
						flex: 1;
					}

					.placeholder {
						font-size: 26rpx;
						font-weight: 400;
						letter-spacing: 0px;
						color: rgba(144, 149, 178, 1);
					}
				}
			}
		}
	}

	.uni-badge {
		padding: 4rpx 20rpx !important;
		height: auto !important;
	}

	.rui-fixed-bottom {
		display: grid;
		padding: 5rpx 28rpx 0;
		box-sizing: border-box;
		grid-template-columns: 40% calc(60% - 40rpx);
		column-gap: 40rpx;
	}
</style>