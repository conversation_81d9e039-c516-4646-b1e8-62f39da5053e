<template>
	<view class="task-detail">
		<!-- 任务基本信息 -->
		<view class="task-info">
			<view class="task-header">
				<text class="task-title">{{detail.title}}</text>
				<text class="task-status" :class="'status-' + detail.status">{{getStatusText(detail.status)}}</text>
			</view>
			<view class="task-meta">
				<view class="meta-item">
					<text class="label">积分奖励：</text>
					<text class="value points">+{{detail.rewardPoints}}</text>
				</view>
				<view class="meta-item">
					<text class="label">发布时间：</text>
					<text class="value">{{detail.taskCreateTime}}</text>
				</view>
				<view class="meta-item">
					<text class="label">接取时间：</text>
					<text class="value">{{detail.acceptTime}}</text>
				</view>
			</view>
		</view>

		<!-- 任务描述 -->
		<view class="description-section">
			<text class="section-title">任务描述</text>
			<text class="description-text">{{detail.description}}</text>
		</view>

		<!-- 任务进度时间轴 -->
		<view class="timeline">
			<view class="timeline-item" :class="{active: detail.status >= 1}">
				<view class="timeline-dot"></view>
				<view class="timeline-content">
					<text class="timeline-title">接取任务</text>
					<text class="timeline-time">{{detail.acceptTime}}</text>
				</view>
			</view>
			<view class="timeline-item" :class="{active: detail.status >= 2}">
				<view class="timeline-dot"></view>
				<view class="timeline-content">
					<text class="timeline-title">提交证明</text>
					<text class="timeline-time">{{detail.submitTime || '待完成'}}</text>
				</view>
			</view>
			<view class="timeline-item" :class="{active: detail.status >= 3}">
				<view class="timeline-dot"></view>
				<view class="timeline-content">
					<text class="timeline-title">审核完成</text>
					<text class="timeline-time">{{detail.reviewTime || '待审核'}}</text>
				</view>
			</view>
		</view>

		<!-- 证明材料（如果有） -->
		<view class="proof-section" v-if="detail.proofText || detail.proofImages">
			<text class="section-title">证明材料</text>
			<view class="proof-text" v-if="detail.proofText">
				<text class="proof-label">文字说明</text>
				<text class="proof-content">{{detail.proofText}}</text>
			</view>
			<view class="proof-images" v-if="detail.proofImages">
				<text class="proof-label">图片证明</text>
				<view class="image-list">
					<view v-for="(img, imgIndex) in getFieldImages(field.id)" 
						:key="imgIndex" 
						class="image-item">
						<image :src="img" 
							mode="aspectFill" 
							@tap="previewImageSimple"
							:data-img-src="img"
							:data-field-id="field.id"></image>
						<view class="delete-btn" 
							@click="handleDeleteImage" 
							:data-field-id="field.id" 
							:data-img-index="imgIndex">×</view>
					</view>
					<view class="upload-btn" @tap="chooseImage(field)" v-if="!getFieldImages(field.id) || getFieldImages(field.id).length < 9">
						<text class="upload-icon">+</text>
						<text class="upload-text">上传图片</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 审核结果（如果有） -->
		<view class="review-section" v-if="detail.reviewStatus">
			<text class="section-title">审核结果</text>
			<view class="review-content">
				<view class="review-status">
					<text class="review-label">审核状态：</text>
					<text class="review-value" :class="detail.reviewStatus === 1 ? 'review-passed' : 'review-rejected'">
						{{detail.reviewStatus === 1 ? '通过' : '不通过'}}
					</text>
				</view>
				<view class="review-time">
					<text class="review-label">审核时间：</text>
					<text class="review-value">{{detail.reviewTime}}</text>
				</view>
				<view class="review-comment" v-if="detail.reviewComment && detail.reviewComment.trim()">
					<text class="review-label">审核意见：</text>
					<text class="review-value">{{detail.reviewComment}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="action-bar" v-if="detail.status === 1 || detail.status === 2">
			<!-- status为1（进行中）时的按钮 -->
			<template v-if="detail.status === 1">
				<button class="action-btn submit" @click="showSubmitProof">提交证明</button>
				<button class="action-btn complete" @click="completeTask" v-if="allRequiredFieldsSubmitted()">任务完成</button>
				<button class="action-btn abandon" @click="showAbandonDialog">放弃任务</button>
			</template>
			
			<!-- status为2（待审核）时的按钮 -->
			<template v-if="detail.status === 2">
				<button class="action-btn cancel-review" @click="cancelReview">取消提交审核</button>
			</template>
		</view>

		<!-- 提交证明弹窗 -->
		<uni-popup ref="submitPopup" type="bottom">
			<view class="submit-form">
				<view class="form-header">
					<text class="form-title">提交证明</text>
					<text class="close-btn" @click="closeSubmitPopup">×</text>
				</view>
				<view class="form-content">
					<view v-for="(field, index) in detail.taskFormFieldsList" :key="field.id" class="form-item">
						<view class="field-header" @tap="toggleField(field)">
							<view class="field-title">
								<text class="field-label">{{field.fieldLabel}}</text>
								<text v-if="field.isRequired" class="required">*</text>
								<text v-if="submittedFields.has(field.id)" class="submitted-tag">已提交</text>
							</view>
							<text class="toggle-icon" :class="{'is-open': openFields.includes(field.id)}">></text>
						</view>
						
						<view class="field-content" v-show="openFields.includes(field.id)">
							<!-- 图片上传字段 -->
							<view v-if="field.fieldType === 'IMAGE'" class="upload-box">
								<view class="image-list">
									<view v-for="(img, imgIndex) in getFieldImages(field.id)" 
										:key="imgIndex" 
										class="image-item">
										<image :src="img" 
											mode="aspectFill" 
											@tap="previewImageSimple"
											:data-img-src="img"
											:data-field-id="field.id"></image>
										<view class="delete-btn" @click="handleDeleteImage" :data-field-id="field.id" :data-img-index="imgIndex">×</view>
									</view>
									<view class="upload-btn" @tap="chooseImage(field)" v-if="!getFieldImages(field.id) || getFieldImages(field.id).length < 9">
										<text class="upload-icon">+</text>
										<text class="upload-text">上传图片</text>
									</view>
								</view>
								<view class="example-image" v-if="field.exampleImageUrl">
									<text class="example-label">示例图片：</text>
									<image :src="baseUrl + field.exampleImageUrl" 
										mode="aspectFill" 
										@click="previewImage(baseUrl + field.exampleImageUrl)"></image>
								</view>
							</view>
							
							<!-- 文本域字段 -->
							<textarea v-if="field.fieldType === 'TEXTAREA'"
								v-model="field.taskUserSubmissionData.submittedTextValue" 
								class="form-textarea" 
								:placeholder="field.placeholderText || '请输入' + field.fieldLabel"
								maxlength="500"></textarea>
							
							<view class="field-footer">
								<button class="field-submit-btn" 
									:disabled="getFieldDisabled(field) || submittedFields.has(field.id)"
									@click="submitField(field)">
									{{submittedFields.has(field.id) ? '已提交' : '提交'}}
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 放弃任务弹窗 -->
		<uni-popup ref="abandonPopup" type="bottom">
			<view class="abandon-form">
				<view class="form-header">
					<text class="form-title">放弃任务</text>
					<text class="close-btn" @click="closeAbandonPopup">×</text>
				</view>
				<view class="form-content">
					<view class="form-item">
						<text class="form-label">放弃原因</text>
						<textarea v-model="detail.abandonReason" 
							class="form-textarea" 
							placeholder="请输入放弃任务的原因"
							maxlength="500"></textarea>
					</view>
				</view>
				<view class="form-footer">
					<button class="submit-btn abandon" @click="submitAbandon">确认放弃</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { getMyTask,updateMyTask,updateTaskData,addTaskData } from "@/api/common.js"
	import { baseUrl } from "../../../config";
    import { getToken } from "../../../utils/auth";
	getToken()
	export default {
		data() {
			return {
				detail: null,
				baseUrl,
				fieldImages: {}, // 用于存储每个字段的图片
				submittedFields: new Set(), // 用于记录已提交的字段
				openFields: [], // 用于记录展开的字段ID
				imageCache: {} // 新增：用于缓存图片路径
			}
		},
		onLoad({id}) {
			console.log(id);
			this.getDetail(id)
		},
		methods: {
			getDetail(id){
				getMyTask(id).then(res=>{
					this.detail = res.data
					// 初始化字段的提交数据对象
					if (this.detail && this.detail.taskFormFieldsList) {
						this.detail.taskFormFieldsList.forEach(field => {
							if (!field.taskUserSubmissionData) {
								field.taskUserSubmissionData = {}
							}
						})
					}
				})
			},
			getStatusText(status) {
				const statusMap = {
					0: '待接取',
					1: '进行中',
					2: '待审核',
					3: '已完成',
					4: '未通过',
					5: '已放弃'
				}
				return statusMap[status] || '未知状态'
			},
			handlePreviewImage(event) {
				const fieldId = event.currentTarget.dataset.fieldId;
				const imgIndex = parseInt(event.currentTarget.dataset.imgIndex);
				
				console.log('预览图片', fieldId, imgIndex);
				
				const images = this.getFieldImages(fieldId);
				if (images && images.length > 0) {
					uni.previewImage({
						current: images[imgIndex],
						urls: images
					});
				}
			},
			previewImage(current) {
				// 保留原有的预览方法，用于示例图片等
				console.log('预览图片:', current);
				let urls = [current];
				
				uni.previewImage({
					current,
					urls
				});
			},
			showSubmitProof() {
				this.$refs.submitPopup.open()
			},
			closeSubmitPopup() {
				this.$refs.submitPopup.close()
			},
			getFieldImages(fieldId) {
				const field = this.detail.taskFormFieldsList.find(f => f.id === fieldId);
				let images = [];
				
				if (field && field.taskUserSubmissionData && field.taskUserSubmissionData.submittedImageUrl) {
					images = field.taskUserSubmissionData.submittedImageUrl.split(',')
						.filter(img => img) // 过滤空字符串
						.map(img => img.startsWith('http') ? img : this.baseUrl + img);
				} else if (this.fieldImages[fieldId]) {
					images = this.fieldImages[fieldId].map(img => this.baseUrl + img);
				}
				
				return images;
			},
			chooseImage(field) {
				uni.chooseImage({
					count: 9,
					success: (res) => {
						const tempFilePaths = res.tempFilePaths
						this.uploadImages(tempFilePaths, field)
					}
				})
			},
			uploadImages(tempFilePaths, field) {
				// 显示加载中
				uni.showLoading({
					title: '上传中...',
					mask: true
				});
				
				const uploadTasks = tempFilePaths.map(filePath => {
					return new Promise((resolve, reject) => {
						uni.uploadFile({
							url: this.baseUrl + '/common/upload',
							filePath: filePath,
							name: 'file',
							success: (res) => {
								try {
									const data = JSON.parse(res.data);
									resolve(data.fileName);
								} catch (e) {
									reject(new Error('解析响应数据失败'));
								}
							},
							fail: (err) => {
								reject(err);
							}
						});
					});
				});
				
				Promise.all(uploadTasks)
					.then(urls => {
						// 初始化fieldImages
						if (!this.fieldImages[field.id]) {
							this.fieldImages[field.id] = [];
						}
						
						// 更新图片数组
						this.fieldImages[field.id] = [...this.fieldImages[field.id], ...urls];
						
						// 更新字段的提交数据
						if (!field.taskUserSubmissionData) {
							field.taskUserSubmissionData = {};
						}
						field.taskUserSubmissionData.submittedImageUrl = this.fieldImages[field.id].join(',');
						
						// 强制更新视图
						this.$forceUpdate();
						
						// 隐藏加载中
						uni.hideLoading();
						
						uni.showToast({
							title: '上传成功',
							icon: 'success'
						});
					})
					.catch(err => {
						// 隐藏加载中
						uni.hideLoading();
						
						uni.showToast({
							title: '图片上传失败',
							icon: 'none'
						});
						console.error('上传失败:', err);
					});
			},
			handleDeleteImage(event) {
				const fieldId = event.currentTarget.dataset.fieldId;
				const imgIndex = parseInt(event.currentTarget.dataset.imgIndex);
				
				console.log('删除图片', fieldId, imgIndex);
				
				const field = this.detail.taskFormFieldsList.find(f => f.id == fieldId);
				if (!field) {
					console.log('未找到字段');
					return;
				}
				
				// 获取当前的图片列表
				let currentImages = [];
				if (field.taskUserSubmissionData && field.taskUserSubmissionData.submittedImageUrl) {
					currentImages = field.taskUserSubmissionData.submittedImageUrl.split(',').filter(img => img);
				} else if (this.fieldImages[fieldId]) {
					currentImages = [...this.fieldImages[fieldId]];
				}
				
				// 删除指定索引的图片
				if (currentImages.length > imgIndex) {
					currentImages.splice(imgIndex, 1);
					
					// 更新数据
					if (!field.taskUserSubmissionData) {
						field.taskUserSubmissionData = {};
					}
					field.taskUserSubmissionData.submittedImageUrl = currentImages.join(',');
					
					// 更新本地fieldImages
					this.fieldImages[fieldId] = currentImages;
					
					// 强制更新视图
					this.$forceUpdate();
					
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				}
			},
			submitField(field) {
				if (!field.taskUserSubmissionData) {
					field.taskUserSubmissionData = {}
				}
				
				if (field.fieldType === 'IMAGE') {
					if (!field.taskUserSubmissionData.submittedImageUrl) {
						uni.showToast({
							title: `请上传${field.fieldLabel}`,
							icon: 'none'
						})
						return
					}
				} else {
					if (!field.taskUserSubmissionData.submittedTextValue) {
						uni.showToast({
							title: `请填写${field.fieldLabel}`,
							icon: 'none'
						})
						return
					}
				}
				
				// 构建提交数据
				const submitData = {
					taskFormFieldId: field.id
				}
				
				// 根据字段类型设置不同的值
				if (field.fieldType === 'IMAGE') {
					submitData.submittedImageUrl = field.taskUserSubmissionData.submittedImageUrl
				} else {
					submitData.submittedTextValue = field.taskUserSubmissionData.submittedTextValue
				}
				
				// 判断是新增还是更新
				if (field.taskUserSubmissionData.id) {
					// 更新提交数据
					submitData.id = field.taskUserSubmissionData.id
					updateTaskData(submitData).then(res => {
						uni.showToast({
							title: '更新成功',
							icon: 'success'
						})
						this.submittedFields.add(field.id)
						this.checkAllFieldsSubmitted()
					}).catch(err => {
						uni.showToast({
							title: '更新失败',
							icon: 'none'
						})
					})
				} else {
					// 新增提交数据
					addTaskData(submitData).then(res => {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						// 保存返回的数据
						field.taskUserSubmissionData = {
							...submitData,
							id: res.data
						}
						this.submittedFields.add(field.id)
						this.checkAllFieldsSubmitted()
					}).catch(err => {
						uni.showToast({
							title: '提交失败',
							icon: 'none'
						})
					})
				}
			},
			checkAllFieldsSubmitted() {
				const allRequiredFieldsSubmitted = this.detail.taskFormFieldsList.every(field => {
					if (field.isRequired) {
						return this.submittedFields.has(field.id)
					}
					return true
				})
				
				if (allRequiredFieldsSubmitted) {
					// 所有必填字段都已提交，更新任务状态
					this.updateTaskStatus()
				}
			},
			updateTaskStatus() {
				// 更新任务状态为待审核
				this.detail.status = 2
				// 格式化日期为 yyyy-MM-dd HH:mm:ss
				const now = new Date()
				const year = now.getFullYear()
				const month = String(now.getMonth() + 1).padStart(2, '0')
				const day = String(now.getDate()).padStart(2, '0')
				const hours = String(now.getHours()).padStart(2, '0')
				const minutes = String(now.getMinutes()).padStart(2, '0')
				const seconds = String(now.getSeconds()).padStart(2, '0')
				this.detail.submitTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
				
				updateMyTask(this.detail).then(res => {
					uni.showToast({
						title: '任务提交完成',
						icon: 'success'
					})
					this.closeSubmitPopup()
					this.getDetail(this.detail.id)
				}).catch(err => {
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					})
				})
			},
			showAbandonDialog() {
				this.$refs.abandonPopup.open()
			},
			closeAbandonPopup() {
				this.$refs.abandonPopup.close()
			},
			submitAbandon() {
				if (!this.detail.abandonReason || !this.detail.abandonReason.trim()) {
					uni.showToast({
						title: '请输入放弃原因',
						icon: 'none'
					})
					return
				}
				
				// 更新任务状态为已放弃
				this.detail.status = 5
				
				updateMyTask(this.detail).then(res => {
					uni.showToast({
						title: '已放弃任务',
						icon: 'success'
					})
					this.closeAbandonPopup()
					this.getDetail(this.detail.id)
				}).catch(err => {
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				})
			},
			toggleField(field) {
				const index = this.openFields.indexOf(field.id);
				if (index > -1) {
					this.openFields.splice(index, 1);
				} else {
					this.openFields.push(field.id);
					// 展开时清除缓存，强制重新计算
					this.imageCache[field.id] = null;
				}
			},
			getFieldDisabled(field) {
				if (!field.taskUserSubmissionData) {
					return true
				}
				
				if (field.fieldType === 'IMAGE') {
					return !field.taskUserSubmissionData.submittedImageUrl || field.taskUserSubmissionData.submittedImageUrl.trim() === ''
				} else {
					return !field.taskUserSubmissionData.submittedTextValue || field.taskUserSubmissionData.submittedTextValue.trim() === ''
				}
			},
			allRequiredFieldsSubmitted() {
				if (!this.detail || !this.detail.taskFormFieldsList) {
					return false
				}
				
				return this.detail.taskFormFieldsList.every(field => {
					if (field.isRequired) {
						if (!field.taskUserSubmissionData) {
							return false
						}
						
						if (field.fieldType === 'IMAGE') {
							return field.taskUserSubmissionData.submittedImageUrl && 
								   field.taskUserSubmissionData.submittedImageUrl.trim() !== ''
						} else {
							return field.taskUserSubmissionData.submittedTextValue && 
								   field.taskUserSubmissionData.submittedTextValue.trim() !== ''
						}
					}
					return true
				})
			},
			completeTask() {
				uni.showModal({
					title: '确认提交',
					content: '确定要完成任务并提交审核吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新任务状态为待审核
							const updateData = {
								...this.detail,
								status: 2
							}
							
							// 格式化提交时间
							const now = new Date()
							const year = now.getFullYear()
							const month = String(now.getMonth() + 1).padStart(2, '0')
							const day = String(now.getDate()).padStart(2, '0')
							const hours = String(now.getHours()).padStart(2, '0')
							const minutes = String(now.getMinutes()).padStart(2, '0')
							const seconds = String(now.getSeconds()).padStart(2, '0')
							updateData.submitTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
							
							updateMyTask(updateData).then(res => {
								uni.showToast({
									title: '任务已提交审核',
									icon: 'success'
								})
								this.getDetail(this.detail.id)
							}).catch(err => {
								uni.showToast({
									title: '提交失败',
									icon: 'none'
								})
							})
						}
					}
				})
			},
			cancelReview() {
				uni.showModal({
					title: '确认取消',
					content: '确定要取消提交审核吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新任务状态为进行中
							const updateData = {
								...this.detail,
								status: 1,
								submitTime: null
							}
							
							updateMyTask(updateData).then(res => {
								uni.showToast({
									title: '已取消提交审核',
									icon: 'success'
								})
								this.getDetail(this.detail.id)
							}).catch(err => {
								uni.showToast({
									title: '操作失败',
									icon: 'none'
								})
							})
						}
					}
				})
			},
			previewImageSimple(event) {
				// 从data属性获取图片地址和字段ID
				const imgSrc = event.currentTarget.dataset.imgSrc;
				const fieldId = event.currentTarget.dataset.fieldId;
				
				console.log('预览图片:', imgSrc, '字段ID:', fieldId);
				
				if (imgSrc && fieldId) {
					// 获取该字段的所有图片
					const allImages = this.getFieldImages(fieldId);
					
					uni.previewImage({
						current: imgSrc,
						urls: allImages
					});
				} else {
					uni.showToast({
						title: '无法获取图片信息',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style lang="scss">
.task-detail {
	padding: 32rpx;
	padding-bottom: 120rpx;
}

.task-info {
	background: #fff;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.task-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.task-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2c3e50;
	flex: 1;
}

.task-status {
	padding: 8rpx 24rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	font-weight: 500;
	
	&.status-0 {
		background: #f1c40f;
		color: #fff;
	}
	
	&.status-1 {
		background: #3498db;
		color: #fff;
	}
	
	&.status-2 {
		background: #9b59b6;
		color: #fff;
	}
	
	&.status-3 {
		background: #2ecc71;
		color: #fff;
	}
	
	&.status-4 {
		background: #e74c3c;
		color: #fff;
	}
}

.task-meta {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	
	.label {
		color: #7f8c8d;
		width: 160rpx;
	}
	
	.value {
		color: #2c3e50;
		
		&.points {
			color: #e67e22;
			font-weight: 600;
		}
	}
}

// 时间轴
.timeline {
	position: relative;
	margin: 40rpx 0;
	padding: 0 32rpx;
	
	.timeline-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 30rpx;
		position: relative;
		opacity: 0.4;
		
		&.active {
			opacity: 1;
		}
		
		&:last-child {
			margin-bottom: 0;
		}
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 15rpx;
			top: 40rpx;
			width: 2rpx;
			height: 50rpx;
			background: #e0e0e0;
		}
		
		&.active:not(:last-child)::after {
			background: #3498db;
		}
		
		.timeline-dot {
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
			background: #e0e0e0;
			margin-right: 20rpx;
			margin-top: 5rpx;
			flex-shrink: 0;
		}
		
		&.active .timeline-dot {
			background: #3498db;
			box-shadow: 0 0 0 4rpx rgba(52, 152, 219, 0.2);
		}
		
		.timeline-content {
			flex: 1;
			
			.timeline-title {
				font-size: 28rpx;
				color: #2c3e50;
				font-weight: 600;
				display: block;
				margin-bottom: 8rpx;
			}
			
			.timeline-time {
				font-size: 24rpx;
				color: #95a5a6;
			}
		}
	}
}

// 描述、证明、审核区域共用样式
.description-section, .proof-section, .review-section {
	padding: 32rpx;
	background: #fff;
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 30rpx;
	color: #2c3e50;
	font-weight: 600;
	margin-bottom: 20rpx;
	display: block;
	padding-bottom: 10rpx;
	border-bottom: 2rpx solid #e0e0e0;
}

.description-text {
	font-size: 28rpx;
	color: #555;
	line-height: 1.7;
	white-space: pre-wrap;
}

// 证明材料
.proof-text, .proof-images {
	margin-bottom: 20rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.proof-label {
	font-size: 26rpx;
	color: #3498db;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.proof-content {
	font-size: 28rpx;
	color: #555;
	line-height: 1.6;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.image-item {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	
	image {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
		border: 2rpx solid #e0e0e0;
	}
	
	.delete-btn {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 48rpx;
		height: 48rpx;
		background: rgba(231, 76, 60, 0.8);
		color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		line-height: 1;
		z-index: 10;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		cursor: pointer;
		
		&:active {
			background: rgba(231, 76, 60, 1);
			transform: scale(0.9);
		}
	}
}

.upload-box {
	width: 100%;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	resize: none;
	
	&:focus {
		border-color: #3498db;
		outline: none;
	}
}

.proof-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 12rpx;
	border: 2rpx solid #e0e0e0;
}

// 审核结果
.review-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.review-status, .review-time, .review-comment {
	display: flex;
	align-items: flex-start;
	gap: 16rpx;
}

.review-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	flex-shrink: 0;
	width: 140rpx;
}

.review-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
	
	&.review-passed {
		color: #27ae60;
		font-weight: 600;
	}
	
	&.review-rejected {
		color: #e74c3c;
		font-weight: 600;
	}
}

// 底部操作栏
.action-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 24rpx 32rpx;
	background: #fff;
	box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
	display: flex;
	gap: 24rpx;
	
	.action-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 28rpx;
		font-weight: 500;
		
		&.submit {
			background: #3498db;
			color: #fff;
		}
		
		&.abandon {
			background: #fff;
			color: #e74c3c;
			border: 2rpx solid #e74c3c;
		}
		
		&.complete {
			background: #2ecc71;
			color: #fff;
		}
		
		&.cancel-review {
			background: #9b59b6;
			color: #fff;
		}
	}
}

// 提交证明表单样式
.submit-form {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
}

.form-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.form-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
}

.close-btn {
	font-size: 40rpx;
	color: #95a5a6;
	padding: 10rpx;
}

.form-content {
	max-height: 60vh;
	overflow-y: auto;
}

.form-item {
	margin-bottom: 32rpx;
}

.field-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	cursor: pointer;
}

.field-title {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.field-label {
	font-size: 28rpx;
	color: #2c3e50;
	font-weight: 500;
}

.toggle-icon {
	font-size: 24rpx;
	color: #95a5a6;
	transform: rotate(90deg);
	transition: transform 0.3s;
	
	&.is-open {
		transform: rotate(-90deg);
	}
}

.submitted-tag {
	font-size: 24rpx;
	color: #27ae60;
	background: rgba(39, 174, 96, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}

.field-content {
	padding: 0 24rpx 24rpx;
}

.field-footer {
	margin-top: 32rpx;
	display: flex;
	justify-content: center;
}

.field-submit-btn {
	width: 80%;
	height: 88rpx;
	line-height: 88rpx;
	background: #3498db;
	color: #fff;
	font-size: 32rpx;
	border-radius: 44rpx;
	text-align: center;
	font-weight: 500;
	box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
	
	&:disabled {
		background: #bdc3c7;
		box-shadow: none;
	}
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 6rpx rgba(52, 152, 219, 0.3);
	}
}

// 放弃任务表单样式
.abandon-form {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
}

.submit-btn.abandon {
	background: #e74c3c;
}

.required {
	color: #e74c3c;
	margin-left: 4rpx;
}

.example-image {
	margin-top: 16rpx;
	
	.example-label {
		font-size: 24rpx;
		color: #95a5a6;
		margin-bottom: 8rpx;
		display: block;
	}
	
	image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		border: 2rpx solid #e0e0e0;
	}
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	background: #f8f9fa;
	border: 2rpx dashed #3498db;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	
	.upload-icon {
		font-size: 48rpx;
		color: #3498db;
		line-height: 1;
	}
	
	.upload-text {
		font-size: 24rpx;
		color: #3498db;
	}
	
	&:active {
		background: #e8f4fc;
	}
}
</style>
