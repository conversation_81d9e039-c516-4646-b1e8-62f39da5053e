<template>
	<view class="container">
		<rui-nav-bar title="我的卡包" back></rui-nav-bar>
		<rui-vtab v-model="currentTab" :tabs="tabs" overspread @change="$store.dispatch('user/GetInfo')"></rui-vtab>
		<!-- <rui-tab v-model="currentTab" :tabs="tabs" bg="#fff"></rui-tab> -->
		<view class="coupons">
			<rui-coupon v-for="(item,index) in currentList" :key="index" :type="getType(item)" :data="item.couponPackage" @click="()=>handleDetail(item)"></rui-coupon>
			<view class="no-more">没有更多了</view>
		</view>
		<uni-popup ref="popup" type="center" border-radius="10px 10px 0 0">
			<view class="card-wrap">
				<canvas id="qrcode" canvas-id="qrcode" style="width: 260px;height:260px;"></canvas>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { mapGetters } from 'vuex'
	export default {
		computed: {
			...mapGetters(["coupons"]),
			currentList() {
				return this.coupons.length ? this.coupons.filter(item => item.status == this.currentTab) : []
			}
		},
		data() {
			return {
				tabs: [
					{ label: "未使用", value: "0" },
					{ label: "已使用", value: "1" },
				],
				currentTab: "0",
				navHegiht: "90px",
			}
		},

		onShow() {
			this.$store.dispatch('user/GetInfo')
		},
		methods: {
			getType(data){
				if(data.status==='1' && this.isDateInRange(data.couponPackage.useStart,data.couponPackage.useEnd)){
					return 'use'
				}
				return 'expired'
			},
			isDateInRange(startDateStr, endDateStr) {
			  const currentDate = new Date();
			  const startDate = new Date(startDateStr);
			  const endDate = new Date(endDateStr);
			
			  // 检查当前日期是否在开始日期和结束日期之间（包括开始和结束）
				console.log('isDateInRange',currentDate >= startDate && currentDate <= endDate)
			  return currentDate >= startDate && currentDate <= endDate;
			},
			handleDetail(item) {
				uni.navigateTo({
					url: "/pages/coupon/detail?couponId=" + item.couponId
				})
			},
			calculateEndTime(startTime, daysToAdd) {
				let date = new Date(startTime);
				date.setDate(date.getDate() + daysToAdd);
				const padZero = (num) => (num < 10 ? '0' : '') + num;
				let endYear = date.getFullYear();
				let endMonth = padZero(date.getMonth() + 1); // 月份从0开始，+1表示真实月份
				let endDay = padZero(date.getDate());
				let endHour = padZero(date.getHours());
				let endMinute = padZero(date.getMinutes());
				return `${endYear}.${endMonth}.${endDay} ${endHour}:${endMinute} 到期`;
			},
		}
	}
</script>

<style lang="scss">
	.container {
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #F8F9FA 26%);
		box-sizing: border-box;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.coupons {
		padding: 40rpx 28rpx;
		flex: 1;
		overflow-y: scroll;
		padding-bottom: 100rpx;

		.no-more {
			padding: 100rpx 0;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			color: #999;
		}

	}
</style>