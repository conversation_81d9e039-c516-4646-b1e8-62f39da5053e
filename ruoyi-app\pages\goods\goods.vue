<template>
	<view class="container">
		<rui-nav-bar title="分类" back></rui-nav-bar>
		<view class="search-wrap">
			<input v-model="searchText" placeholder="搜索积分商品" />
			<view class="search-icon" @click="handleSearch">
				<uni-icons type="search" size="25" color="#fff"></uni-icons>
			</view>
		</view>
		<rui-tab v-model="activeTab" :tabs="tabs" @change="handleSearch"></rui-tab>
		<view class="goods-list">
			<view class="goods-item" @click="handleDetail(goods)" v-for="(goods,goodsIndex) in goodsList">
				<rui-image-preview class="goods-cover" :preview="false" :src="goods.goodsImage" radius="24rpx"
					width="140rpx" height="140rpx"></rui-image-preview>
				<view class="goods-info">
					<view class="goods-title">{{goods.goodsName}}</view>
					<view class="goods-residue">剩余{{goods.goodsBalance}}件</view>
					<view class="goods-row">
						<view class="goods-integral">{{goods.goodsIntegral}}<text style="font-size: 20rpx;">积分</text> </view>
						<rui-button type="primary" height="40rpx" size="22" v-if="goods.goodsBalance>0" @click="handleDetail(goods)">立即兑换</rui-button>
						<view class="goods-soldout" v-else>已售罄</view>
					</view>
				</view>
			</view>
			
			<view class="no-goods" v-if="!goodsList.length">暂无商品</view>
		</view>
	</view>
</template>

<script>
	import { goodsGroup } from '@/api/common.js'
	export default {
		computed: {
			tabs() {
				return this.categories.map(item => {
					return  item.goods.length? `${item.name}(${item.goods.length>99?'99+':item.goods.length})`:item.name
				})
			},
			goodsList() {
				return this.filtterGroups[this.activeTab] ? this.filtterGroups[this.activeTab].goods :[]
			}
		},
		watch: {
			searchText() {
				this.handleSearch()
			}
		},
		data() {
			return {
				activeTab: 0,
				tabHieght: 0,
				searchText: "",
				categories: [],
				filtterGroups: [],
			};
		},
		onLoad() {
			this.goodsGroup()
			const query = uni.createSelectorQuery().in(this);
			query.select('.screen-wrap').boundingClientRect((data) => {
				if (data) {
					this.tabHieght = data.height + "px"
				}
			}).exec();
		},
		methods: {
			handleDetail(item) {
				uni.navigateTo({
					url: "/pages/goods/detail?goodsId=" + item.goodsId
				})
			},
			handleSearch() {
				let data = JSON.parse(JSON.stringify(this.categories))
				if (this.searchText) {
					this.filtterGroups = data.map(group => {
						group.goods = group.goods.filter(goods => goods.goodsName.toLowerCase().includes(this.searchText
							.toLowerCase()));
						return group
					})
				} else {
					this.filtterGroups = data
				}
			},
			goodsGroup() {
				goodsGroup().then(res => {
					this.categories = res.data
					this.activeTab = res.data.findIndex(item =>item.goods.length)
					this.handleSearch()
				})
			},
		}
	}
</script>

<style lang="less">
	.fui-vtabs__content-wrap{
		overflow: hidden;
	}
	.container {
		height: 100vh;
		overflow: hidden;
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #fff 26%);
		display: flex;
		flex-direction: column;

		.rui-nav-bar {
			flex-shrink: 0;
		}


		.search-wrap {
			margin: 20rpx 28rpx;
			border: 1rpx solid rgba(198, 109, 29, 1);
			background-color: #fff;
			border-radius: 82rpx;
			height: 82rpx;
			display: flex;
			align-items: center;
			padding-left: 34rpx;
			padding-right: 10rpx;
			opacity: .8;
			flex-shrink: 0;

			input {
				flex: 1;
				font-size: 26rpx;
				color: #333333;
			}

			.search-icon {
				width: 130rpx;
				height: 86%;
				background: #C66D1D;
				border-radius: 32rpx 32rpx 32rpx 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.goods-list {
			flex: 1;
			height: 100%;
			padding: 20rpx 24rpx env(safe-area-inset-bottom);
			box-sizing: border-box;
			overflow-y: scroll;

			.goods-item {
				border-radius: 28rpx;
				background: rgba(255, 255, 255, 1);
				box-shadow: 0px -6rpx 12rpx rgba(173, 174, 179, 0.09), 0px 6rpx 12px rgba(173, 174, 179, 0.09);
				display: flex;
				margin-bottom: 32rpx;
				padding: 10rpx 10rpx;

				.goods-cover {
					border-radius: 20rpx;
					margin-right: 24rpx;
				}

				.goods-soldout {
					color: red;
					font-size: 24rpx;
					padding-right: 4rpx;
				}

				.goods-info {
					flex: 1;
					box-sizing: border-box;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					overflow: hidden;

					.goods-title {
						font-size: 32rpx;
						color: #333333;
						line-height: 48rpx;
						text-align: left;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.goods-residue {
						flex: 1;
						font-size: 24rpx;
						color: rgba(70, 74, 87, 1);
					}

					.goods-row {
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding-bottom: 4rpx;

						.goods-integral {
							color: #FF6600;
							font-size: 38rpx;
							font-weight: 400;
						}
					}
				}
			}
		}
		.no-goods{
			text-align: center;
			margin: 100rpx auto;
			color: #555555;
		}
	}

</style>