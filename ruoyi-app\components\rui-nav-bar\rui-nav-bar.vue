<template>
	<view class="rui-nav-bar" :style="style">
		<view :style="'height:'+barHeight+'px'"></view>
		<view class="nav-wrap">
			<uni-icons class="rui-nav-bar_back" v-if="back" type="left" size="26" :color="color" @click="handleBack"></uni-icons>
			<text class="rui-nav-bar_title" :style="titleStyle">{{title}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-nav-bar",
		computed: {
			barHeight(){
				return uni.getSystemInfoSync().statusBarHeight
			},
			style(){
				return `position:${this.fixed?'fixed':'relative'};color: ${this.color};z-index:99;`
			},
			titleStyle(){
				if(this.align ==="center"){
					return `position: absolute;left:50%;transform: translateX(-50%);`
				}
			}
		},
		props: {
			title: {
				type: String,
				default: ""
			},
			fixed:{
				type:Boolean,
				default:false
			},
			back:{
				type:Boolean,
				default:false
			},
			align:{
				type: String,
				default: "center"
			},
			color:{
				type: String,
				default: "#333333"
			}
		},
		methods:{
			handleBack(){
				uni.navigateBack({
					fail() {
						uni.reLaunch({
							url:"/pages/layout/index"
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.rui-nav-bar {
		color: #333333;
		font-size: 28rpx;
		font-weight: 500;
		letter-spacing: 0px;
		position: fixed;
		width: 100%;
		box-sizing: border-box;
		.nav-wrap{
			padding: 0 28rpx;
			height: 74rpx;
			position: relative;
			display: flex;
			align-items: center;
			.rui-nav-bar_back{
				position: absolute;
				left: 28rpx;
			}
			.rui-nav-bar_title{
				white-space: nowrap;
			}
		}
	}
</style>