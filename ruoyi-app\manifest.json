{
    "name" : "积分商城",
    "appid" : "__UNI__EC0DED7",
    "description" : "应用描述",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "OAuth" : {},
            "Payment" : {},
            "Push" : {},
            "Share" : {},
            "Speech" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : []
            },
            /* ios打包配置 */
            "ios" : {
                "UIBackgroundModes" : [ "audio" ]
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "speech" : {
                    "ifly" : {}
                }
            },
            "orientation" : [ "portrait-primary" ]
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxadc718cb975318c6",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : true,
            "es6" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "获取用户位置导航至店铺店铺"
            }
        }
    },
    "h5" : {
        "template" : "template.h5.html",
        "router" : {
            "mode" : "history",
            "base" : "/h5/"
        }
    }
}
