<template>
	<view class="container">
		<rui-card margin="0 28rpx" bg="#fff">
			<rui-cell label="头像" padding="0 0">
				<button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
					<view style="width: 90rpx;height: 90rpx;">
						<rui-avatar :src="user.avatar" size="90rpx" :key="user.avatar"></rui-avatar>
					</view>
				</button>
			</rui-cell>
			<rui-cell label="昵称" placeholder="昵称1~16个字符" :content="user.nickName">
				<input :value="user.nickName" type="nickname" style="width: 100%;" placeholder="昵称1~16个字符"
					@blur="handleEditNickName" @confirm="handleEditNickName"
					@change="(e)=>user.nickName = e.detail.value"></input>
			</rui-cell>
			<rui-cell label="性别" arrow placeholder="选择性别" :content="user.sex==1?'女':'男'" @click="showSelect"></rui-cell>
			<rui-cell label="手机" arrow placeholder="点击绑定" :content="user.phonenumber" ></rui-cell>
			<rui-cell label="邮箱" arrow placeholder="点击更改" :content="user.email" @click="handleToEditInfo"></rui-cell>
			<rui-cell label="密码" arrow placeholder="点击更改" @click="handleEditPsw"></rui-cell>
		</rui-card>
		<rui-card margin="28rpx 28rpx" bg="#fff">
			<rui-cell label="权限设置" arrow placeholder="点击管理" @click="handleSetting"></rui-cell>
			<!-- <rui-cell label="清除缓存" arrow placeholder="点击清除"></rui-cell> -->
			<rui-cell label="客服电话" arrow :placeholder="appInfo.phone" @click="handleCall"></rui-cell>
			<!-- <rui-cell label="账号注销" arrow placeholder="注销	后无法恢复"></rui-cell> -->
		</rui-card>
		<rui-card margin="0 28rpx 28rpx" bg="#fff">
			<rui-cell label="隐私政策" arrow @click="handlePrivacy"></rui-cell>
			<rui-cell label="用户服务协议" arrow @click="handleUserAgrement"></rui-cell>
			<rui-cell label="意见反馈" arrow height="101rpx">
				<button class="feedback-btn" open-type="feedback"></button>
			</rui-cell>
		</rui-card>
		<view class="cell-btn" @click="handleLogout">退出登录</view>
		<rui-select :show="sexShow" :list="sexOption" height="200" title="请选择性别" @confirm="onConfirm"
			@close="onClose"></rui-select>
	</view>
</template>

<script>
	import { updateUserProfile, uploadAvatar } from '@/api/system/user'
	export default {
		data() {
			return {
				sexShow: false,
				sexOption: [{
						text: '男',
						value: "0",
						checked: false,
					},
					{
						text: '女',
						value: "1",
						checked: false,
					}
				],
				appInfo: getApp().globalData.config.appInfo,
				user: {}
			}
		},
		onLoad() {
			this.getInfo()
		},
		methods: {
			getInfo() {
				this.$store.dispatch('user/GetInfo').then((res) => {
					this.user = res.user
				})
			},
			showSelect() {
				this.sexOption = this.sexOption.map(item => {
					item.checked = item.value == this.user.sex
					return item
				})
				this.sexShow = true
			},
			onClose() {
				this.sexShow = false
			},
			onConfirm(e) {
				if (e.options.value) {
					updateUserProfile({ ...this.user, sex: e.options.value }).then(response => {
						this.sexShow = false
						this.getInfo()
					})
				}
			},
			async onChooseAvatar(e) {
				const res = await uploadAvatar({
					filePath: e.detail.avatarUrl,
					name: "avatarfile"
				});
				if(res.code===200)this.getInfo()
			},
			handleEditNickName(e) {
				setTimeout(() => {
					updateUserProfile({nickName:this.user.nickName}).then(res => {
						this.getInfo()
					})
				})
			},
			handleEditPsw() {
				uni.navigateTo({
					url: '/pages/mine/pwd/index'
				})
			},
			handleSetting() {
				uni.openSetting({ withSubscriptions: true })
			},
			handleToEditInfo() {
				uni.navigateTo({
					url: '/pages/mine/info/edit'
				})
			},
			handleToPwd() {
				uni.navigateTo({
					url: '/pages/mine/pwd/index'
				})
			},
			handleCall() {
				uni.makePhoneCall({
					phoneNumber: this.appInfo.phone
				})
			},
			// 隐私协议
			handlePrivacy() {
				uni.navigateTo({
					url:`/pages/contract/contract?title=隐私协议&type=privacy_contract`
				})
			},
			// 用户协议
			handleUserAgrement() {
				uni.navigateTo({
					url:`/pages/contract/contract?title=用户协议&type=user_contract`
				})
			},
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要注销并退出系统吗？',
					success: (res) => {
						if (res.confirm) {
							this.$store.dispatch('user/LogOut').then(() => {
								this.$store.commit('app/setTabbarIndex', 0);
								uni.reLaunch({
									url:' /pages/layout/index'
								})
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F9FA;
	}

	.container {
		padding-top: 28rpx;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.cell-btn {
		text-align: center;
		padding: 28rpx 0;
		background-color: #fff;
		color: #ee0a24;
		font-size: 26rpx;

		&:active {
			opacity: .9;
		}
	}

	.avatar-wrapper {
		display: inline-block;
		background-color: transparent;
		padding: 0 !important;
		border: none;
		height: 90rpx;

		&::after {
			display: none;
		}
	}

	.feedback-btn {
		background-color: transparent !important;
		height: 101rpx;
		width: 100%;

		&::after {
			display: none;
		}
	}
</style>