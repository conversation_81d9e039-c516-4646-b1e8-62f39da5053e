<template>
  <view class="about-container">
    <view class="header-section text-center">
      <image style="width: 150rpx;height: 150rpx;" src="@/static/logo.png" mode="widthFix">
      </image>
      <uni-title type="h2" title="积分商城"></uni-title>
    </view>

    <view class="content-section">
      <view class="menu-list">
        <view class="list-cell">
          <view class="menu-item-box">
            <view>版本信息</view>
            <view class="text-right">v{{version}}</view>
          </view>
        </view>
        <view class="list-cell">
          <view class="menu-item-box">
            <view>官方邮箱</view>
            <view class="text-right">{{email}}</view>
          </view>
        </view>
        <view class="list-cell">
          <view class="menu-item-box">
            <view>服务热线</view>
            <view class="text-right">{{phone}}</view>
          </view>
        </view>
        <view class="list-cell">
          <view class="menu-item-box">
            <view>公司网站</view>
            <view class="text-right">
              <uni-link :href="url" :text="url" showUnderLine="false"></uni-link>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="copyright">
      <view>Copyright &copy; 2024 积分商城 All Rights Reserved.</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        url: getApp().globalData.config.appInfo.site_url,
        version: getApp().globalData.config.appInfo.version,
        email: getApp().globalData.config.appInfo.email,
        phone: getApp().globalData.config.appInfo.phone,
      }
    }
  }
</script>

<style lang="scss">
  page {
  	background-color: rgba(248, 249, 250, 1);
  }

  .copyright {
    margin-top: 150rpx;
    text-align: center;
    line-height: 60rpx;
    color: #555555;
		font-size: 24rpx;
  }

  .header-section {
    display: flex;
    padding: 30rpx 0 0;
    flex-direction: column;
    align-items: center;
  }
	.text-right{
		margin-right: 0 !important;
	}
</style>
