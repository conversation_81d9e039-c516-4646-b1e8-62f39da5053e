<template>
	<view class="container">
		<image class="topbg" src="@/static/images/topbg.png" mode="widthFix"></image>
		<rui-nav-bar back color="#fff"></rui-nav-bar>
		<main>
			<view class="main-head">
				<block v-if="form.status==='0'">
					<view>
						<image src="@/static/images/icon/status_0.png" mode="widthFix"></image>
						<text>待核销</text>
					</view>
					<view>请在规定时间内使用</view>
				</block>
				<block v-if="form.status==='1'">
					<view>
						<image src="@/static/images/icon/status_0.png" mode="widthFix"></image>
						<text>已核销</text>
					</view>
					<view>核销时间：{{form.useTime}}</view>
				</block>
			</view>
			<rui-card title="基本信息">
				<rui-describe label="优惠卷编号">{{form.couponId}}</rui-describe>
				<rui-describe label="优惠金额"><text class="price">{{form.couponPackage.discountAmount}}</text><text
						style="margin-left: 6rpx;">元</text>
				</rui-describe>
			</rui-card>
			<rui-card title="优惠卷信息">
				<rui-describe label="商品名称" :content="form.couponPackage.packageName"></rui-describe>
				<rui-describe label="使用门槛"
					:content="form.couponPackage.thresholdAmount===0?'无门槛':`满${form.couponPackage.thresholdAmount}可用`"></rui-describe>
				<rui-describe label="有效开始时间" :content="form.couponPackage.useStart"></rui-describe>
				<rui-describe label="有效结束时间" :content="form.couponPackage.useEnd"></rui-describe>
			</rui-card>
			<rui-card title="领取信息">
				<rui-describe label="领取账号" :content="form.userName"></rui-describe>
				<rui-describe label="领取时间" :content="form.createTime"></rui-describe>
				<rui-describe label="来源" :content="form.remark"></rui-describe>
			</rui-card>
			<rui-card title="核销信息" v-if="form.status==='1'">
				<rui-describe label="核销账号" :content="form.updateBy"></rui-describe>
				<rui-describe label="核销时间" :content="form.useTime"></rui-describe>
			</rui-card>
		</main>
		<uni-popup ref="popup" type="center" border-radius="10px 10px 0 0" @maskClick="getCoupon">
			<view class="card-wrap">
				<image style="width: 260px;" :src="qrCodeSrc" mode="widthFix"></image>
			</view>
		</uni-popup>
		<rui-fixed-bottom>
			<rui-button height="96rpx" width="260rpx" @click="handleBack">返回</rui-button>
			<rui-button type="success" width="240rpx" height="96rpx" @click="handleCode"
				v-if="form.status==='0'&&!scene">出示兑换码</rui-button>
			<rui-button v-else-if="form.status==='0'" type="success" width="400rpx;" height="96rpx"
				@click="handleSubmit">确认核销</rui-button>
		</rui-fixed-bottom>
		<zero-loading type="atom" mask v-if="loading" mask-opacity="0.6"></zero-loading>
	</view>
</template>

<script>
	import { getCoupon, couponComplete, createQRCode } from "@/api/common.js";
	import {mapGetters} from 'vuex'
	export default {
		computed:{
			...mapGetters(["stores"]),
		},
		data() {
			return {
				form: {},
				loading: true,
				qrCodeSrc: "",
				scene: false,
			};
		},
		onLoad(option) {
			if (option.scene) {
				this.scene = true
				const { couponId, s } = this.parsePathAndParams(option.scene)
				this.form.couponId = couponId
			} else {
				this.scene = false
				this.form.couponId = option.couponId
			}
			this.getCoupon()
		},
		methods: {
			handleSubmit() {
				if(!this.stores.length){
					uni.showToast({
						title: '当前账户不支持核销！',
						icon: 'none'
					})
					return
				}
				uni.showModal({
					title: '提示',
					content: '确定核销吗？',
					success: (res) => {
						if (res.confirm) {
							couponComplete({ couponId: this.form.couponId,storeId:this.stores[0].storeId }).then(res => {
								if (res.code === 200) {
									uni.showToast({
										title: '核销成功',
										icon: 'none'
									})
									this.getCoupon()
								}
							})
						}
					}
				})
			},
			parsePathAndParams(scene) {
				let result = {};
				let decodedScene = decodeURIComponent(scene);
				while (decodedScene !== decodeURIComponent(decodedScene)) {
					decodedScene = decodeURIComponent(decodedScene);
				}
				decodedScene.split('&').forEach(param => {
					let [key, value] = param.split('=');
					result[key] = value;
				});
				return result;
			},
			handleCode() {
				this.loading = true
				const params = {
					page: "pages/coupon/detail",
					width: 260,
					env_version: uni.getAccountInfoSync().miniProgram.envVersion,
					scene: `s=1&couponId=${this.form.couponId}`,
				}
				createQRCode(params).then(res => {
					const base64 = uni.arrayBufferToBase64(res.data);
					this.qrCodeSrc = `data:image/jpeg;base64,${base64}`;
					this.$refs.popup.open()
					this.loading = false
				}).catch(() => {
					this.loading = false
					uni.showToast({
						title: "二维码生成失败",
						icon: "error"
					})
				})
			},
			handleBack() {
				uni.navigateBack({
					fail() {
						uni.reLaunch({
							url:"/pages/layout/index"
						})
					}
				})
			},
			getCoupon() {
				this.loading = true
				getCoupon(this.form.couponId).then(res => {
					if (res.code === 200) {
						this.form = res.data
					}
				}).finally(() => {
					this.loading = false
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: rgba(248, 249, 250, 1);
	}

	.container {
		height: 100vh;
		overflow-y: scroll;

		.topbg {
			position: absolute;
			width: 100%;
			float: left;
			z-index: -1;
		}

		main {
			padding: 8% 30rpx 180rpx;

			.main-head {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding-bottom: 50rpx;
				color: #fff;

				view:nth-child(1) {
					font-size: 34rpx;
					line-height: 60rpx;
					display: flex;
					align-items: center;

					image {
						width: 34rpx;
						height: 34rpx;
						margin-right: 10rpx;
					}
				}

				view:nth-child(2) {
					font-size: 24rpx;
					font-weight: 400;
					line-height: 34rpx;
				}
			}
		}
	}

	.price {
		color: red;
		font-size: 40rpx;
		font-weight: bold;
	}

	.rui-fixed-bottom {
		display: flex;
		justify-content: flex-end;
		padding-right: 28rpx;
		padding-top: 10rpx;

		.rui-button {
			margin-left: 20rpx;
		}
	}
</style>