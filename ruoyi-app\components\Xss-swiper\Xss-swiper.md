

		import XsuuSwiper from "../../components/Xsuu-swiper.vue"
		components: {XsuuSwiper}
		
-----------------------------------------


		// swiperItems  循环对象
		// height  高度
		// margin  边距
		// borderRadius 圆角
		// titleFontSize 标题大小
		// titleColor 标题颜色
		// SubtitleFontSize 副标题大小
		// SubtitleColor 副标题颜色
		// dotHeight 指示点距离轮播底部高度
		// dotMargin 指示点侧边距
		// SelectBG 指示点未选中颜色
		// UncheckedBG 指示点选中颜色
		// DotPosition 指示点位置 1左侧 2右侧 3居中
		// button 查看详情按钮是否显示 1显示 0不显示
		// previousMargin 前边距，可用于露出前一项的一小部分
		// nextMargin 后边距，可用于露出后一项的一小部分