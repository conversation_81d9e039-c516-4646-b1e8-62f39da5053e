<template>
	<view class="rui-upload">
		<!-- 已上传文件 -->
		<view class="rui-upload_box" :style="style" v-for="(item,index) in _value" :key="index">
			<view class="rui-upload_close" @click="handleRemove('_value',index)">
				<uni-icons type="closeempty" color="#fff" size="12"></uni-icons>
			</view>
			<view class="mark" :style="{borderRadius:radius}" v-if="item.status=='loading'||item.status=='error'">
				<uni-icons class="loading-icon" type="spinner-cycle" size="24" color="#fff"
					v-if="item.status=='loading'"></uni-icons>
				<uni-icons type="refreshempty" size="24" color="#fff" @click="onUpload(item)" v-else></uni-icons>
			</view>
			<image @click="handlePriview(index)" :src="getUrl(item)" lazy-load :style="style"></image>
		</view>
		<view class="rui-upload_box" :style="style">
			<view class="rui-upload_placeholder" @click="beforeUpload">
				<uni-icons type="camera" size="30" color="#D2D4DD"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-upload",
		props: {
			value: {
				type: [String, Array],
				default: []
			},
			radius: {
				type: String,
				default: "16rpx"
			},
			width: {
				type: String,
				default: "150rpx"
			},
			height: {
				type: String,
				default: "150rpx"
			},
			action: {
				type: String,
				require: true
			},
			headers: {
				type: Object,
				default: () => {}
			},
			limit: {
				type: Number,
				default: 9
			},
			sizeType: {
				type: Array,
				default: () => ["original"] // original原图 compressed 压缩
			},
			sourceType: {
				type: Array,
				default: () => ["album", "camera"] // album从相册选图，camera使用相机
			},
			formData: {
				type: Object,
				default: () => {}
			},
			autoUpload: {
				type: Boolean,
				default: true
			},
			baseUrl: {
				type: String,
				default: ""
			}
		},
		computed: {
			_value: {
				get() {
					return this.value
				},
				set(val) {
					this.$emit("input", val)
				}
			},
			style() {
				return `width:${this.width};height:${this.height};border-radius:${this.radius};`
			}
		},
		methods: {
			handlePriview(index) {
				uni.previewImage({
					urls: this._value.map(item => this.getUrl(item)),
					current: index
				})
			},
			getUrl(item) {
				if (item.url && (item.status === "loading" || item.status === "no_upload" || item.status === "error")) {
					return item.url
				}
				return this.baseUrl + (item.url ? item.url : item)
			},
			beforeUpload() {
				uni.chooseImage({
					count: this.limit,
					sizeType: this.sizeType,
					sourceType: this.sourceType,
					success: ({ tempFilePaths, tempFiles }) => {
						const files = tempFilePaths.map(item => {
							return {
								url: item,
								status: this.autoUpload ? "loading" : "no_upload",
								id: new Date().valueOf()+Math.random()
							}
						})
						this._value = [...this._value, ...files];
						setTimeout(() => {
							if (this.autoUpload) {
								files.forEach(this.onUpload)
							}
						}, )
					}
				})
			},
			onUpload(file) {
				this.updateStatus(file.id, "loading")
				uni.uploadFile({
					url: this.baseUrl + this.action,
					filePath: file.url,
					name: 'file',
					formData: this.formData,
					header: this.headers,
					success: (res) => {
						const data = JSON.parse(res.data);
						if (data.code === 200) {
							console.log(this._value);
							this._value = this._value.map(item => {
								return item.id === file.id ? data.fileName : item
							})
						} else {
							this.updateStatus(file.id, "error")
						}
					},
					fail: (error) => {
						console.log(error);
						this.updateStatus(file.id, "error")
					}
				});
			},
			updateStatus(id, status) {
				this._value = this.value.map(item => {
					return item.id && item.id === id ? { ...item, status } : item
				})
			},
			handleRemove(key, index) {
				this[key] = this[key].filter((a, i) => !(index == i))
			},
			onSuccess({ tempFilePaths, tempFiles, errMsg }) {

			},
			onFail() {},
			onComplete() {}
		}
	}
</script>

<style lang="scss">
	.rui-upload {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
	}

	.rui-upload_box {
		position: relative;
		background: rgba(248, 249, 250, 1);

		image {
			width: 100%;
			height: 100%;
		}

		.rui-upload_close {
			position: absolute;
			z-index: 9;
			top: -6rpx;
			right: -6rpx;
			border-radius: 50%;
			width: 25rpx;
			height: 25rpx;
			line-height: 25rpx;
			text-align: center;
			background-color: rgba(0, 0, 0, 0.2);
		}
	}

	.rui-upload_placeholder {
		background: rgba(248, 249, 250, 1);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;

		&:active {
			opacity: .9;
		}
	}

	.mark {
		position: absolute;
		inset: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(0, 0, 0, 0.4);
		z-index: 8;

		.loading-icon {
			animation: rotate 2s linear infinite !important;
		}
	}

	@keyframes rotate {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}
</style>