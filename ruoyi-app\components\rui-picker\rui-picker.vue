<template>
	<view class="rui-picker">
		<input v-model="_value" :placeholder="placeholder" disabled placeholder-class="placeholder" @click="handleSelect" />
		<view class="rui-picker_mask" :class="{'rui-picker_show':visible}" @click="cancel">
			<view class="rui-picker_popup">
				<view class="picker-head">
					<view class="popup-head_cancel" @tap.stop="cancel">取消</view>
					<view class="popup-head_title">{{title}}</view>
					<view class="popup-head_confirm" @tap.stop="confirm">确认</view>
				</view>
				<view class="picker-content">
					<picker-view class="picker-view" indicator-class="current-column" :value="[currnetValue]" @change="change">
						<picker-view-column>
							<view class="picker-view-column_text" v-for="(item,index) in range" :key="index">{{ item[rangeKey] }}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { dict } from "@/api/login";
	export default {
		name: "rui-picker",
		options: { styleIsolation: 'shared' },
		props: {
			dictType: { type: String },
			value: { type: String, default: "" },
			title: { type: String, default: "请选择" },
			placeholder: { type: String, default: "请选择" },
			columnHeight: {
				type: String,
				default: '180rpx'
			},
			option: {
				type: Array,
				default: () => []
			}
		},
		computed: {
			_value: {
				get() { return this.value },
				set(val) { this.$emit('input', val) }
			},
			range() {
				if (this.dictType) {
					return this.dictData
				} else {
					return this.option
				}
			},
			rangeKey() {
				return this.dictType ? 'dictLabel' : 'label'
			}
		},
		data() {
			return {
				dictData: [],
				visible: false,
				currnetValue: 0
			};
		},
		watch: {
			dictType: {
				immediate: true,
				handler(val) {
					if (!val) return
					dict(val).then(res => {
						this.dictData = res.data
					})
				}
			}
		},
		methods: {
			handleSelect() {
				this.visible = true
			},
			change(e) {
				this.currnetValue = e.detail.value[0]
			},
			confirm() {
				this._value = this.range[this.currnetValue][this.rangeKey]
				this.visible = false
			},
			cancel() {
				this.visible = false
			},
		}
	}
</script>

<style lang="scss">
	.rui-picker_mask {
		position: fixed;
		inset: 0;
		z-index: 1314;
		// transition: all .5s;
		// display: none;
		visibility: hidden;
		background-color: rgba(0, 0, 0, 0.1);
		opacity: 0;
		transition: opacity 0.3s, visibility 0.3s;

		.rui-picker_popup {
			border-top-left-radius: 32rpx;
			border-top-right-radius: 32rpx;
			background-color: #fff;
			position: fixed;
			bottom: -100%;
			left: 0;
			right: 0;
			background-color: white;
			min-height: 500rpx;
			padding-bottom: env(safe-area-inset-bottom);
			display: flex;
			flex-direction: column;
			transition: bottom .5s;

			.picker-head {
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 88rpx;

				.popup-head_cancel {
					color: #969799;
					padding: 0 32rpx;

					&:active {
						opacity: .9;
					}
				}

				.popup-head_title {
					color: #323233;
					max-width: 50%;
					font-weight: bold;
				}

				.popup-head_confirm {
					color: #1989fa;
					padding: 0 32rpx;

					&:active {
						opacity: .9;
					}
				}
			}

			.picker-content {
				height: 100%;
				width: 100%;
				flex: 1;
			}
		}
	}

	.rui-picker_show {
		opacity: 1;
		visibility: visible;
		background-color: rgba(0, 0, 0, 0.5);

		.rui-picker_popup {
			bottom: 0;
		}
	}

	.picker-view {
		height: 300rpx;

		picker-view-column {
			height: 100%;

			.picker-view-column_text {
				color: #323233;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>