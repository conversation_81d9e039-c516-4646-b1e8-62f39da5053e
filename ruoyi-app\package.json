{"name": "ruoyi-app", "version": "1.0.0", "description": "<p align=\"center\">\r \t<img alt=\"logo\" src=\"https://oscimg.oschina.net/oscnet/up-43e3941654fa3054c9684bf53d1b1d356a1.png\">\r </p>\r <h1 align=\"center\" style=\"margin: 30px 0 30px; font-weight: bold;\">RuoYi v1.1.0</h1>\r <h4 align=\"center\">基于UniApp开发的轻量级移动端框架</h4>\r <p align=\"center\">\r \t<a href=\"https://gitee.com/y_project/RuoYi-App/stargazers\"><img src=\"https://gitee.com/y_project/RuoYi-App/badge/star.svg?theme=dark\"></a>\r \t<a href=\"https://gitee.com/y_project/RuoYi-App\"><img src=\"https://img.shields.io/badge/RuoYi-v1.1.0-brightgreen.svg\"></a>\r \t<a href=\"https://gitee.com/y_project/RuoYi-App/blob/master/LICENSE\"><img src=\"https://img.shields.io/github/license/mashape/apistatus.svg\"></a>\r </p>", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-App.git"}, "keywords": [], "author": "", "license": "ISC", "uni-app": {"scripts": {"wx-dev": {"title": "微信小程序-本地", "env": {"UNI_PLATFORM": "mp-weixin", "VUE_APP_BASE_API": "http://localhost:8080"}}, "wx-pro": {"title": "微信小程序-生产", "env": {"UNI_PLATFORM": "mp-weixin", "VUE_APP_BASE_API": "https://jf.yrscpp.com/prod-api"}}}}, "dependencies": {"qrcode": "^1.5.4", "uqrcodejs": "^4.0.7", "weapp-qrcode": "^1.0.0"}}