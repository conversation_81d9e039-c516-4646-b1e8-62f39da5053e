<template>
	<view class="rui-divider">
		<view class="rui-divider-line"></view>
		<view class="rui-divider-text">{{text}}</view>
		<view class="rui-divider-line"></view>
	</view>
</template>

<script>
	export default {
		name:"rui-divider",
		props:{
			text:{
				type:String,
				default:""
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss">
.rui-divider{
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 20rpx 0;
	.rui-divider-line{
		width: 140rpx;
		height: 1rpx;
		background-color: #dcdee0;
	}
	.rui-divider-text{
		margin: 0 20rpx;
		color: #999;
		font-size: 24rpx;
	}
}
</style>