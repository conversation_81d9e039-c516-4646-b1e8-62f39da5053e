const getters = {
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  coupons: state => state.user.coupons,
  stores: state => state.user.stores,
	coupon: state => state.user.coupons&&state.user.coupons.filter(a=>a.status==='0'),
	couponCount: state => state.user.coupons&&state.user.coupons.filter(a=>a.status==='0').length || 0,
	integralCount: state => state.user.user&&state.user.user.integral || 0
}
export default getters
