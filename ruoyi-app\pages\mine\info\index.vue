<template>
	<view class="container">
		<!-- 		<uni-list>
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'person-filled'}" title="昵称" :rightText="user.nickName" />
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'phone-filled'}" title="手机号码"
				:rightText="user.phonenumber" />
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'email-filled'}" title="邮箱" :rightText="user.email" />
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="岗位" :rightText="postGroup" />
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'staff-filled'}" title="角色" :rightText="roleGroup" />
			<uni-list-item showExtraIcon="true" :extraIcon="{type: 'calendar-filled'}" title="创建日期"
				:rightText="user.createTime" />
		</uni-list> -->

		<rui-card margin-bottom="150rpx">
			<rui-describe label="头像" padding="0 0 18rpx"><rui-avatar :src="avatar" size="100rpx"></rui-avatar></rui-describe>
			<rui-describe label="账号" topBorder>{{user.userName}}</rui-describe>
			<rui-describe label="昵称" topBorder>{{user.phonenumber}}</rui-describe>
			<rui-describe label="邮箱" topBorder>{{user.email}}</rui-describe>
			<rui-describe label="手机号码" topBorder>{{user.phonenumber}}</rui-describe>
			<rui-describe label="角色" topBorder :content="roles"></rui-describe>
			<rui-describe label="创建日期" padding="18rpx 0 0" topBorder :content="user.createTime"></rui-describe>
		</rui-card>
	</view>
</template>

<script>
	import { getUserProfile } from "@/api/system/user"

	export default {
		computed: {
			avatar() {
				return this.$store.state.user.avatar
			},
			roles() {
				return this.user.roles ? this.user.roles.map(item => item.roleName).join("、") : ''
			}
		},
		data() {
			return {
				user: {},
				roleGroup: "",
				postGroup: ""
			}
		},
		onLoad() {
			this.getUser()
		},
		methods: {
			getUser() {
				getUserProfile().then(response => {
					this.user = response.data
					this.roleGroup = response.roleGroup
					this.postGroup = response.postGroup
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: rgba(248, 249, 250, 1);
	}

	.container {
		padding: 28rpx;

		.rui-describe {
			padding: 40rpx 0;
		}

		.rui-describe_label {
			color: #333333 !important;
			font-size: 26rpx !important;
		}

		.rui-describe_content {
			color: #666666 !important;
		}
	}
</style>