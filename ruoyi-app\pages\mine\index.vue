<template>
	<view class="container">
		<rui-nav-bar title="我的"></rui-nav-bar>
		<view class="head" v-if="token">
			<rui-avatar :src="avatar"></rui-avatar>
			<view class="info" @click="handleToInfo">
				<view class="nickName">{{nickName}}</view>
				<view class="badge">{{userName}}</view>
			</view>
			<view class="edit" @click="handleToEditInfo">
				<image src="../../static/images/icon/edit.png" mode=""></image>
				<text>编辑资料</text>
			</view>
		</view>

		<view class="noLogin" v-else>
			<image src="@/static/logo.png"></image>
			<view class="tip">
				<text>小程序用户</text>
				<text>为您提供更好的服务请授权登录</text>
			</view>
			<rui-button @click="handleShowLogin" height="54rpx" width="150rpx" size="20">登录/注册</rui-button>
		</view>

		<view class="card-wrap qy-card">
			<!-- <view class="card-wrap_title">
				<view class="text">我的权益</view>
				<view class="more-button" @click="handleMore">
					<text>查看权益</text>
					<uni-icons type="right" size="12" color="rgba(0, 0, 0, 0.4)"></uni-icons>
				</view>
			</view> -->
			<view class="qy-info">
				<view class="info-item">
					<text>{{integralCount}}</text>
					<text>我的积分</text>
				</view>
				<!-- <view class="line"></view> -->
				<!-- <view class="info-item" @click="checkLoginTo('/pages/coupon/coupon')">
					<text>{{couponCount}}</text>
					<text>我的卡卷</text>
				</view> -->
			</view>
		</view>

		<view class="card-wrap">
			<view class="card-wrap_title">我的订单</view>
			<view class="card-wrap_grid">
				<!-- <view class="grid-item" @click="checkLoginTo('/pages/mine/myTask/myTask')">
					<image src="../../static/images/icon/order_01.png"></image>
					<text>我的任务</text>
				</view> -->
				<view class="grid-item" v-if="stores.length" @click="checkLoginTo('/pages/integral/record')">
					<image src="../../static/images/icon/order_04.png"></image>
					<text>核销记录</text>
				</view>
				<view class="grid-item" @click="checkLoginTo('/pages/mine/verification/verification')">
					<image src="../../static/images/icon/order_02.png"></image>
					<text>核销</text>
				</view>
				<view class="grid-item" @click="checkLoginTo('/pages/mine/order/order')">
					<image src="../../static/images/icon/order_03.png"></image>
					<text>商品兑换</text>
				</view>
				<view class="grid-item" @click="handleScan" v-if="stores.length || hasRole">
					<image src="../../static/images/icon/scan-full.png"></image>
					<text>扫码核销</text>
				</view>
			</view>
		</view>
		<view class="card-wrap">
			<view class="card-wrap_title">其他服务</view>
			<view class="card-wrap_grid">
				<!-- <view class="grid-item" @click="checkLoginTo('/pages/mine/group/group')">
					<image src="../../static/images/icon/jsrz.png"></image>
					<text>加入社群</text>
				</view> -->
				<view class="grid-item" @click="checkLoginTo('/pages/mine/feedback/feedback')">
					<image src="../../static/images/icon/gywm.png"></image>
					<text>投诉建议</text>
				</view>
				<view class="grid-item" @click="handleAbout">
					<image src="../../static/images/icon/cjwt.png"></image>
					<text>关于我们</text>
				</view>
				<view class="grid-item" @click="handleCall">
					<image src="../../static/images/icon/phone.png"></image>
					<text>联系我们</text>
				</view>
				<view class="grid-item" @click="handleLogout" v-if="token">
					<image src="../../static/images/icon/setting.png"></image>
					<text>退出登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapGetters, mapState } from 'vuex'
	import { getToken } from '@/utils/auth'
	export default {
		data() {
			return {
				version: getApp().globalData.config.appInfo.version,
				phone: getApp().globalData.config.appInfo.phone,
			}
		},
		watch: {
			token: {
				handler(newVal) {
					if (newVal) {
						this.$store.dispatch('user/GetInfo')
					}
				},
				immediate: true
			}
		},
		computed: {
			...mapState('user', {
				token: state => state.token,
				user: state => state.user,
				avatar: state => state.user.avatar,
				nickName: state => state.user.nickName,
				userName: state => state.user.userName,
			}),
			hasRole() {
				return this.roles.includes("ROLE_HX")
			},
			...mapGetters(["couponCount", "integralCount", "stores", "roles"])
		},
		methods: {
			checkLoginTo(url) {
				if (!getToken()) {
					this.$store.commit('app/toggleLoginModal', true);
					return false;
				}
				uni.navigateTo({ url });
				return true;
			},
			handleShowLogin() {
				this.$store.commit('app/toggleLoginModal', true);
			},
			handleScan() {
				if (!getToken()) {
					this.$store.commit('app/toggleLoginModal', true);
					return;
				}
				uni.scanCode({
					success: (res) => {
						if (res.scanType === "WX_CODE") {
							uni.navigateTo({
								url: `/${res.path}`
							})
						} else {
							uni.showToast({
								title: '请扫描正确的二维码',
								icon: 'none'
							})
						}
						console.log('扫描结果:', res);
						console.log('扫描到的内容: ' + res.result);
					},
				});
			},
			handleHelp() {
				this.$tab.navigateTo('/pages/mine/feedback/feedback')
			},
			handleGoOrder(status) {
				this.$tab.navigateTo('/pages/mine/order/order')
			},
			handleCall() {
				uni.makePhoneCall({
					phoneNumber: this.phone
				})
			},
			handleAbout() {
				uni.navigateTo({
					url: '/pages/mine/about/index'
				})
			},
			handleToInfo() {
				this.$tab.navigateTo('/pages/mine/info/index')
			},
			handleToEditInfo() {
				this.$tab.navigateTo('/pages/mine/setting/index')
				// this.$tab.navigateTo('/pages/mine/info/edit')
			},
			handleToSetting() {
				this.$tab.navigateTo('/pages/mine/setting/index')
			},
			handleToLogin() {
				this.$tab.reLaunch('/pages/login')
			},
			handleToAvatar() {
				this.$tab.navigateTo('/pages/mine/avatar/index')
			},
			handleLogout() {
				this.$modal.confirm('确定注销并退出系统吗？').then(() => {
					this.$store.dispatch('user/LogOut').then(() => {

					})
				})
			},
		}
	}
</script>

<style lang="scss">
	.container {
		height: 100vh;
		padding-bottom: calc(env(safe-area-inset-bottom) + 50px);
		box-sizing: border-box;
		background: linear-gradient(to bottom, rgba(252, 234, 212, 1) 0%, #F8F9FA 26%);
	}

	.noLogin {
		display: flex;
		align-items: center;
		// background-color: #fff;
		border-radius: 8rpx;
		padding: 30rpx 16rpx;
		margin: 20rpx 32rpx 50rpx;
		border-radius: 20rpx;


		image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			overflow: hidden;
			flex-shrink: 0;
		}

		.tip {
			display: flex;
			flex-direction: column;
			padding: 0 16rpx;
			flex: 1;

			text:nth-child(1) {
				font-size: 28rpx;
				color: #333333;
			}

			text:nth-child(2) {
				margin-top: 8rpx;
				font-size: 24rpx;
			}
		}
	}

	.head {
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		margin-bottom: 50rpx;

		.info {
			padding-left: 32rpx;
			flex: 1;

			.nickName {
				font-size: 32rpx;
				font-weight: 700;
				letter-spacing: 0px;
				line-height: 42rpx;
				color: rgba(55, 57, 72, 1);
			}

			.badge {
				display: inline-block;
				padding: 8rpx 30rpx;
				border-radius: 48rpx;
				background: rgba(242, 169, 34, 0.6);
				font-size: 20rpx;
				font-weight: 500;
				color: #fff;
				margin-top: 10rpx;
			}
		}

		.edit {
			display: flex;
			align-items: center;

			image {
				width: 26rpx;
				height: 26rpx;
				margin-right: 10rpx;
			}

			text {
				font-size: 24rpx;
				font-weight: 500;
				letter-spacing: 0px;
				line-height: 34rpx;
				color: rgba(55, 57, 72, 1);
			}
		}
	}

	.qy-info {
		color: #464a57;
		display: flex;
		align-items: center;

		.info-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			text:nth-child(1) {
				font-size: 38rpx;
				padding-bottom: 5rpx;
			}

			text:nth-child(2) {
				font-size: 24rpx;
			}
		}

		.line {
			height: 60rpx;
			width: 1rpx;
			border-radius: 2rpx;
			background-color: rgba(178, 178, 178, 1);
			margin: 0 10rpx;
		}
	}

	.card-wrap {
		border-radius: 20rpx;
		background: rgba(255, 255, 255, 1);
		box-shadow: 0px 4rpx 10rpx rgba(233, 239, 245, 1);
		padding: 32rpx;
		margin: 0 32rpx 50rpx;

		.card-wrap_title {
			padding-bottom: 32rpx;
			font-size: 16px;
			font-weight: 700;
			letter-spacing: 0px;
			line-height: 23.17px;
			color: rgba(55, 57, 72, 1);
			display: flex;
			align-items: center;
			justify-content: space-between;

			.more-button {
				display: flex;
				align-items: center;
				font-size: 22rpx;
				color: rgba(0, 0, 0, 0.4);
			}
		}

		.card-wrap_grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-row-gap: 24rpx;

			.grid-item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				&:active {
					opacity: .8;
				}

				image {
					width: 48rpx;
					height: 48rpx;
				}

				text {
					padding-top: 10rpx;
					font-size: 24rpx;
					font-weight: 500;
					line-height: 20.27px;
					color: rgba(70, 74, 87, 1);
				}
			}
		}
	}
</style>