<template>
	<view class="rui-vtab"
		:class="{'tab-box':type==='box','tab-indicator':type==='indicator','tab-overspread':overspread}">
		<view :class="{'tab':true,'current-tab':item.value==value}" :style="{height}" v-for="(item,index) in _tabs"
			:key="index" @click="handleTab(item)">{{item.label}}</view>
		<slot v-if="type==='indicator'">
			<view class="indicator" :style="indicatorStyle"></view>
		</slot>
	</view>
</template>

<script>
	export default {
		name: "rui-vtab",
		emits: ["change", "input"],
		computed: {
			_value: {
				get() {
					return this.value
				},
				set(val) {
					this.$emit("input", val)
				}
			},
			_tabs() {
				return this.tabs.map((item, index) => {
					if (typeof item === 'string') {
						return {
							label: item,
							value: index,
							index: index
						}
					} else {
						return {
							...item,
							index: index,
						}
					}
				})
			},
			indicatorStyle() {
				if (!this.nodes.length) return
				let x = 0
				for (let i = 0; i <= this.currentIndex; i++) {
					x += this.currentIndex === i ? (this.nodes[i].width / 2) : this.nodes[i].width
				}
				return `transform: translateX(${x}px);`
			}
		},
		props: {
			value: {
				type: [Number,String],
			},
			type: {
				type: String,
				default: "indicator"
			},
			height: {
				type: String,
				default: "indicator"
			},
			indicatorWidth: {
				type: Number,
				default: 80
			},
			tabs: Array,
			overspread: {
				type: Boolean,
				default: false,
			}
		},
		watch: {
			tabs: {
				handler(newVal, oldVal) {
					this.$nextTick(() => {
						this.getNodes()
					})
				},
				deep: true
			},
			value(newVal, oldVal) {
				this.tabs.forEach((item, index) => {
					if (item.value == newVal) {
						this.currentIndex = index
					}
				})
			}
		},
		data() {
			return {
				nodes: [],
				currentIndex: 0
			}
		},
		mounted() {
			this.getNodes()
		},
		methods: {
			handleTab(item) {
				this.currentIndex = item.index
				this._value = item.value
				this.$emit('change', item)
			},
			getNodes() {
				const query = uni.createSelectorQuery().in(this);
				query.selectAll(".tab").boundingClientRect((data) => {
					this.nodes = data
				}).exec()
			}
		}
	}
</script>

<style lang="scss">
	.rui-vtab {
		display: flex;
		overflow-x: scroll;
		padding: 10rpx 0;

		.tab {
			padding: 0 10rpx 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.tab-indicator {
		position: relative;
	}

	.indicator {
		width: 80rpx;
		height: 4px;
		left: -40rpx;
		opacity: 1;
		border-radius: 3px;
		position: absolute;
		bottom: 0rpx;
		transition: all .3s;
		background: rgba(230, 162, 60, 1);
	}

	.tab-overspread {
		.tab {
			height: 100%;
			flex: 1;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.tab-box {
		.tab {
			padding: 0 32rpx;
			height: 64rpx;
			line-height: 64rpx;
			border-radius: 110rpx;
			opacity: 0.6;
			background: rgba(233, 233, 231, 1);
			color: rgba(51, 51, 51, 1);
			font-size: 24rpx;
			margin-left: 28rpx;
			flex-shrink: 0;
			transition: background-color .2s;
		}

		.current-tab {
			opacity: 1;
			color: #fff;
			background: rgba(76, 110, 245, 1);
			box-shadow: 0px 4rpx 10rpx rgba(76, 110, 245, .5);
		}
	}
</style>