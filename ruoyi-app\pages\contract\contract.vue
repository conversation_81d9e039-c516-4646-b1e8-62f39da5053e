<template>
	<view class="container">
		<mp-html :content="form.content" />
	</view>
</template>

<script>
	import { getContract } from '@/api/common.js'
	export default {
		data() {
			return {
				type: '',
				title:"",
				form:""
			}
		},
		onLoad({ type ,title = ""}) {
			this.type = type
			this.title = title
			uni.setNavigationBarTitle({
				title
			})
			this.getContract()
		},
		methods: {
			getContract() {
				getContract(this.type).then(res => {
					this.form = res.data
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx 20px 100rpx;
	}
</style>