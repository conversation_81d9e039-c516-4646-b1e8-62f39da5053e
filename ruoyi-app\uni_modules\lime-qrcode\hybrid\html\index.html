<!DOCTYPE html>
<html lang="zh">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>lime-qrcode</title>
	<style>
		html,body,canvas {
			margin: 0;
			padding: 0;
			width: 100%;
			height: 100%;
			pointer-events: none;
			/* background-color: rgba(255,0,0,0.1) */
		}
	</style>
</head>
<body>
	<canvas id="lime-qrcode"></canvas>
	<script type="text/javascript" src="./uni.webview.1.5.3.js"></script>
	<script type="text/javascript" src="./qrcode.min.js"></script>
	<script>
		var canvas = document.querySelector('#lime-qrcode')
		var pixelRatio = window.devicePixelRatio || 1
		function appendWatermark(image) {
			emit('append', mark.toDataURL())
		}
		
		var qrcode = new lime.QRCodeCanvas(canvas, {
			pixelRatio,
		})
		function render(props) {
			if(props.pixelRatio) {
				pixelRatio = props.pixelRatio
			}
			if(qrcode) {
				qrcode.render(props)
			} 
		}
		function toDataURL(file) {
			if(qrcode && canvas) {
				try{
					const image = canvas.toDataURL()
					emit('toDataURL', {
						file,
						image
					})
				}catch(e){
					emit('toDataURL', {
						file,
						msg: e
					})
				}
				
			} 
		}
		function emit(event, data) {
			postMessage({
				event,
				data
			});
		};
		function postMessage(data) {
			uni.postMessage({
				data
			});
		};
		// render({
		// 	content: ['Lime UI'],
		// 	// rotate: -22,
		// 	// baseSize: 2,
		// 	// fontGap: 3
		// })
	</script>
</body>
</html>