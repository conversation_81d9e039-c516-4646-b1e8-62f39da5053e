<template>
	<view class="layout">
		<home v-show="tabbarIndex===0"></home>
		<coupon v-show="tabbarIndex===1"></coupon>
		<myTask v-show="tabbarIndex===2"></myTask>
		<mine v-if="tabbarIndex===3"></mine>
		<LoginModal></LoginModal>
		<rui-tabbar :value="tabbarIndex" :tabBar="tabBar" @change="setCurrent"></rui-tabbar>
	</view>
</template>

<script>
	import home from '../index.vue'
	import coupon from '../coupon/index.vue'
	import mine from '../mine/index.vue'
	import myTask from '../mine/myTask/myTask.vue'

	export default {
		components: {
			home,
			coupon,
			mine,
			myTask
		},
		computed: {
			tabbarIndex() {
				return this.$store.state.app.tabbarIndex
			}
		},
		data() {
			return {
				tabBar: [{
					text: "首页",
					iconPath: require("static/images/tabbar/home.png"),
					selectedIconPath: require("static/images/tabbar/home_.png"),
				}, {
					text: "任务中心",
					iconPath: require("static/images/tabbar/coupon.png"),
					selectedIconPath: require("static/images/tabbar/coupon_.png"),
				}, {
					text: "任务进度",
					iconPath: require("static/images/tabbar/jindu.png"),
					selectedIconPath: require("static/images/tabbar/jindu_.png"),
				},
				{
					text: "我的",
					iconPath: require("static/images/tabbar/mine.png"),
					selectedIconPath: require("static/images/tabbar/mine_.png"),
				}],
			}
		},
		onShareAppMessage(res) {
			return {
				title: '积分商城',
				path: '/pages/layout/index'
			}
		},
		methods: {
			setCurrent(index) {
				this.$store.commit('app/setTabbarIndex', index)
			},
			openLoginModal() {
				this.$refs.LoginModal.open()
			},
			closeLoginModal() {
				this.$refs.LoginModal.close()
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		overflow: hidden;
		overscroll-behavior: none;
	}

	.layout {
		height: 100vh;
		position: relative;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
	}
</style>