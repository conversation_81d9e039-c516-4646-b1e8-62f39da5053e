<template>
	<text :class="['rui-tag' , `rui-tag_${type?type:'primary'}`]">
		<slot></slot>
	</text>
</template>

<script>
	export default {
		name: "rui-tag",
		props: {
			type: {
				type: String,
				default: "primary"
			},
			size: {
				type: String,
				default: "default"
			},
			shape: {
				type: String,
				default: "default"
			},
			color: {
				type: String,
				default: "default"
			},
			circle: {
				type: Boolean,
				default: false
			},
			plain: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss">
	.rui-tag {
		background-color: #ecf5ff;
		display: inline-block;
		height: 44rpx;
		padding: 0 20rpx;
		line-height: 44rpx;
		font-size: 22rpx;
		color: #409eff;
		border: 1rpx solid #d9ecff;
		border-radius: 10rpx;
		box-sizing: border-box;
		white-space: nowrap;
	}



	.rui-tag.rui-tag_success {
		background-color: #f0f9eb;
		border-color: #e1f3d8;
		color: #67c23a;
	}

	.rui-tag.rui-tag_info {
		background-color: #f4f4f5;
		border-color: #e9e9eb;
		color: #909399;
	}

	.rui-tag.rui-tag_warning {
		background-color: #fdf6ec;
		border-color: #faecd8;
		color: #e6a23c;
	}

	.rui-tag.rui-tag_danger {
		background-color: #fef0f0;
		border-color: #fde2e2;
		color: #f56c6c;
	}
</style>