import request from '@/utils/request'

export function goodsList(query) {
  return request({
    url: '/app/goods/list',
    method: 'get',
    params: query
  })
}

export function bannerList(query) {
  return request({
    url: '/system/banner/list',
    method: 'get',
    params: {status:'0',pageNum:1,pageSize:30}
  })
}


export function goodsGroup() {
  return request({
    url: '/app/goods/group',
    method: 'get',
  })
}

export function goodsDetail(goodsId) {
  return request({
    url: '/system/IntegralGoods/'+goodsId,
    method: 'get',
  })
}



// 查询优惠券卡包发放列表
export function listCouponPackage(query) {
  return request({
    url: '/app/couponPackage/list',
    method: 'get',
    params: query
  })
}

export function getCouponStores(packageId) {
  return request({
    url: '/system/couponPackage/stores/'+packageId,
    method: 'get',
  })
}

// 新增用户投诉
export function addComplain(data) {
  return request({
    url: '/system/complain',
    method: 'post',
    data: data
  })
}

export function listComplain(query) {
  return request({
    url: '/app/complain/list',
    method: 'get',
    params: query
  })
}
export function receive(data) {
  return request({
    url: '/system/couponData/receive',
    method: 'post',
    data: data
  })
}

// 查询微信群组列表
export function listChatGroup(query) {
  return request({
    url: '/system/ChatGroup/list',
    method: 'get',
    params: query
  })
}

// 查询我的订单
export function listOrder(query) {
  return request({
    url: '/app/order/list',
    method: 'get',
    params: query
  })
}
// 订单取消
export function orderCancel(params) {
  return request({
    url: '/app/order/cancel',
    method: 'get',
		params
  })
}

// 订单详情
export function getOrder(orderId) {
  return request({
    url: '/system/IntegralOrders/'+orderId,
    method: 'get'
  })
}

// 订单支付
export function addOrder(data) {
  return request({
    url: '/system/IntegralOrders',
    method: 'post',
		data
  })
}

// 积分列表
export function listIntegral(query) {
  return request({
    url: '/app/integral/list',
    method: 'get',
    params: query
  })
}

// 生成二维码
export function createQRCode(data) {
  return request({
    url: '/app/create/qrcode',
    method: 'post',
		responseType: 'arraybuffer',
    data: data
  })
}

export function getCoupon(couponId) {
  return request({
    url: '/system/couponData/'+couponId,
    method: 'get',
  })
}


export function getContract(contractType) {
  return request({
    url: '/system/contract/type/'+contractType,
    method: 'get',
  })
}

export function getOrderRecord(params) {
  return request({
    url: '/app/order/record',
    method: 'get',
		params
  })
}

export function getCouponRecord(params) {
  return request({
    url: '/app/coupon/record',
    method: 'get',
		params
  })
}

// 商品核销
export function orderComplete(data) {
  return request({
    url: '/app/order/complete',
    method: 'post',
		data
  })
}


// 优惠卷核销
export function couponComplete(data) {
  return request({
    url: '/app/coupon/complete',
    method: 'post',
		data
  })
}


// 车费信息
export function getParkInfo(params) {
  return request({
    url: '/parking/info',
    method: 'get',
		params
  })
}

// 任务信息
export function getTaskList(params) {
  return request({
    url: '/app/task/list',
    method: 'get',
	params
  })
}

// 任务详情
export function getTask(id) {
  return request({
    url: '/system/tasks/'+id
  })
}

// 接取任务
export function acceptTask(data) {
  return request({
    url: '/app/task/accept',
	method: 'POST',
	data
  })
}

// 我接取的任务
export function myTask(params){
	return request({
		url: '/app/task/mylist',
		params
	})
}

// 我接取的任务详情
export function getMyTask(id){
	return request({
		url: '/system/records/'+id,
	})
}

//修改我接取的任务
export function updateMyTask(data){
	return request({
		url: '/system/records/',
		method:"PUT",
		data
	})
}

// 提交任务项
export function addTaskData(data){
	return request({
		url: '/system/data/',
		method:"POST",
		data
	})
}

//修改任务项
export function updateTaskData(data){
	return request({
		url: '/system/data/',
		method:"PUT",
		data
	})
}

//核销
export function verification(orderId){
	return request({
		url: '/app/verification/'+ orderId
	})
}