<template>
	<view :class="['rui-describe','rui-describe-'+direction]" :style="style">
		<view class="rui-describe_label">
			<slot name="label">
				<text>{{label}}</text>
				<text v-if="suffix">{{suffix}}</text>
			</slot>
		</view>
		<view class="rui-describe_content">
			<slot>
				<text class="rui-describe_content_placeholder" v-if="!content">{{placeholder}}</text>
				<text class="rui-describe_content_text" v-else>{{content}}</text>
			</slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rui-describe",
		props: {
			label: {
				type: String,
				default: ""
			},
			content: {
				type: String,
				default: ""
			},
			placeholder: {
				type: String,
				default: "-"
			},
			topBorder: {
				type: Boolean,
				default: false
			},
			bottomBorder: {
				type: Boolean,
				default: false
			},
			suffix: {
				type: String,
				default: ""
			},
			borderStyle: {
				type: String,
				default: "1rpx solid rgba(236, 236, 236, 1);"
			},
			direction: {
				type: String, // vertical horizontal
				default: "horizontal"
			},
			padding:{
				type:String,
				default:"18rpx 0"
			}
		},
		computed: {
			style() {
				let styleStr = `padding:${this.padding}`
				if (this.topBorder) styleStr += `border-top:${this.borderStyle};`
				if (this.bottomBorder) styleStr += `border-bottom:${this.borderStyle};`
				return styleStr
			}
		}
	}
</script>

<style lang="scss">
	.rui-describe {
		display: flex;
		font-weight: 400;
		letter-spacing: 0rpx;

		.rui-describe_label {
			font-size: 26rpx;
			color: rgba(102, 102, 102, 1);
		}

		.rui-describe_content {
			font-size: 28rpx;
			font-weight: 500;
			letter-spacing: 0px;
			color: #333333;

			.rui-describe_content_placeholder {
				color: rgba(102, 102, 102, 1);
			}

			.rui-describe_content_text {}
		}
	}

	.rui-describe-horizontal {
		justify-content: space-between;
		align-items: center;
	}

	.rui-describe-vertical {
		flex-direction: column;
		.rui-describe_label{
			margin-bottom: 20rpx;
		}
	}
</style>